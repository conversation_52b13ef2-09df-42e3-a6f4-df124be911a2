"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/reselect";
exports.ids = ["vendor-chunks/reselect"];
exports.modules = {

/***/ "(ssr)/./node_modules/reselect/dist/reselect.mjs":
/*!*************************************************!*\
  !*** ./node_modules/reselect/dist/reselect.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSelector: () => (/* binding */ createSelector),\n/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),\n/* harmony export */   createStructuredSelector: () => (/* binding */ createStructuredSelector),\n/* harmony export */   lruMemoize: () => (/* binding */ lruMemoize),\n/* harmony export */   referenceEqualityCheck: () => (/* binding */ referenceEqualityCheck),\n/* harmony export */   setGlobalDevModeChecks: () => (/* binding */ setGlobalDevModeChecks),\n/* harmony export */   unstable_autotrackMemoize: () => (/* binding */ autotrackMemoize),\n/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)\n/* harmony export */ });\n// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {\n  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n    let isInputSameAsOutput = false;\n    try {\n      const emptyObject = {};\n      if (resultFunc(emptyObject) === emptyObject)\n        isInputSameAsOutput = true;\n    } catch {\n    }\n    if (isInputSameAsOutput) {\n      let stack = void 0;\n      try {\n        throw new Error();\n      } catch (e) {\n        ;\n        ({ stack } = e);\n      }\n      console.warn(\n        \"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\",\n        { stack }\n      );\n    }\n  }\n};\n\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {\n  const { memoize, memoizeOptions } = options;\n  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);\n  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n  if (!areInputSelectorResultsEqual) {\n    let stack = void 0;\n    try {\n      throw new Error();\n    } catch (e) {\n      ;\n      ({ stack } = e);\n    }\n    console.warn(\n      \"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\",\n      {\n        arguments: inputSelectorArgs,\n        firstInputs: inputSelectorResults,\n        secondInputs: inputSelectorResultsCopy,\n        stack\n      }\n    );\n  }\n};\n\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n  inputStabilityCheck: \"once\",\n  identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks) => {\n  Object.assign(globalDevModeChecks, devModeChecks);\n};\n\n// src/utils.ts\nvar NOT_FOUND = /* @__PURE__ */ Symbol(\"NOT_FOUND\");\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n  if (typeof object !== \"object\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n  if (!array.every((item) => typeof item === \"function\")) {\n    const itemTypes = array.map(\n      (item) => typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item\n    ).join(\", \");\n    throw new TypeError(`${errorMessage}[${itemTypes}]`);\n  }\n}\nvar ensureIsArray = (item) => {\n  return Array.isArray(item) ? item : [item];\n};\nfunction getDependencies(createSelectorArgs) {\n  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n  assertIsArrayOfFunctions(\n    dependencies,\n    `createSelector expects all input-selectors to be functions, but received the following types: `\n  );\n  return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n  const inputSelectorResults = [];\n  const { length } = dependencies;\n  for (let i = 0; i < length; i++) {\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n  }\n  return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {\n  const { identityFunctionCheck, inputStabilityCheck } = {\n    ...globalDevModeChecks,\n    ...devModeChecks\n  };\n  return {\n    identityFunctionCheck: {\n      shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n      run: runIdentityFunctionCheck\n    },\n    inputStabilityCheck: {\n      shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n      run: runInputStabilityCheck\n    }\n  };\n};\n\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n  revision = $REVISION;\n  _value;\n  _lastValue;\n  _isEqual = tripleEq;\n  constructor(initialValue, isEqual = tripleEq) {\n    this._value = this._lastValue = initialValue;\n    this._isEqual = isEqual;\n  }\n  // Whenever a storage value is read, it'll add itself to the current tracker if\n  // one exists, entangling its state with that cache.\n  get value() {\n    CURRENT_TRACKER?.add(this);\n    return this._value;\n  }\n  // Whenever a storage value is updated, we bump the global revision clock,\n  // assign the revision for this storage to the new value, _and_ we schedule a\n  // rerender. This is important, and it's what makes autotracking  _pull_\n  // based. We don't actively tell the caches which depend on the storage that\n  // anything has happened. Instead, we recompute the caches when needed.\n  set value(newValue) {\n    if (this.value === newValue)\n      return;\n    this._value = newValue;\n    this.revision = ++$REVISION;\n  }\n};\nfunction tripleEq(a, b) {\n  return a === b;\n}\nvar TrackingCache = class {\n  _cachedValue;\n  _cachedRevision = -1;\n  _deps = [];\n  hits = 0;\n  fn;\n  constructor(fn) {\n    this.fn = fn;\n  }\n  clear() {\n    this._cachedValue = void 0;\n    this._cachedRevision = -1;\n    this._deps = [];\n    this.hits = 0;\n  }\n  get value() {\n    if (this.revision > this._cachedRevision) {\n      const { fn } = this;\n      const currentTracker = /* @__PURE__ */ new Set();\n      const prevTracker = CURRENT_TRACKER;\n      CURRENT_TRACKER = currentTracker;\n      this._cachedValue = fn();\n      CURRENT_TRACKER = prevTracker;\n      this.hits++;\n      this._deps = Array.from(currentTracker);\n      this._cachedRevision = this.revision;\n    }\n    CURRENT_TRACKER?.add(this);\n    return this._cachedValue;\n  }\n  get revision() {\n    return Math.max(...this._deps.map((d) => d.revision), 0);\n  }\n};\nfunction getValue(cell) {\n  if (!(cell instanceof Cell)) {\n    console.warn(\"Not a valid cell! \", cell);\n  }\n  return cell.value;\n}\nfunction setValue(storage, value) {\n  if (!(storage instanceof Cell)) {\n    throw new TypeError(\n      \"setValue must be passed a tracked store created with `createStorage`.\"\n    );\n  }\n  storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n  return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n  assertIsFunction(\n    fn,\n    \"the first parameter to `createCache` must be a function\"\n  );\n  return new TrackingCache(fn);\n}\n\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b) => false;\nfunction createTag() {\n  return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n  setValue(tag, value);\n}\nvar consumeCollection = (node) => {\n  let tag = node.collectionTag;\n  if (tag === null) {\n    tag = node.collectionTag = createTag();\n  }\n  getValue(tag);\n};\nvar dirtyCollection = (node) => {\n  const tag = node.collectionTag;\n  if (tag !== null) {\n    dirtyTag(tag, null);\n  }\n};\n\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy(this, objectProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar objectProxyHandler = {\n  get(node, key) {\n    function calculateResult() {\n      const { value } = node;\n      const childValue = Reflect.get(value, key);\n      if (typeof key === \"symbol\") {\n        return childValue;\n      }\n      if (key in proto) {\n        return childValue;\n      }\n      if (typeof childValue === \"object\" && childValue !== null) {\n        let childNode = node.children[key];\n        if (childNode === void 0) {\n          childNode = node.children[key] = createNode(childValue);\n        }\n        if (childNode.tag) {\n          getValue(childNode.tag);\n        }\n        return childNode.proxy;\n      } else {\n        let tag = node.tags[key];\n        if (tag === void 0) {\n          tag = node.tags[key] = createTag();\n          tag.value = childValue;\n        }\n        getValue(tag);\n        return childValue;\n      }\n    }\n    const res = calculateResult();\n    return res;\n  },\n  ownKeys(node) {\n    consumeCollection(node);\n    return Reflect.ownKeys(node.value);\n  },\n  getOwnPropertyDescriptor(node, prop) {\n    return Reflect.getOwnPropertyDescriptor(node.value, prop);\n  },\n  has(node, prop) {\n    return Reflect.has(node.value, prop);\n  }\n};\nvar ArrayTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy([this], arrayProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar arrayProxyHandler = {\n  get([node], key) {\n    if (key === \"length\") {\n      consumeCollection(node);\n    }\n    return objectProxyHandler.get(node, key);\n  },\n  ownKeys([node]) {\n    return objectProxyHandler.ownKeys(node);\n  },\n  getOwnPropertyDescriptor([node], prop) {\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n  },\n  has([node], prop) {\n    return objectProxyHandler.has(node, prop);\n  }\n};\nfunction createNode(value) {\n  if (Array.isArray(value)) {\n    return new ArrayTreeNode(value);\n  }\n  return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n  const { value, tags, children } = node;\n  node.value = newValue;\n  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n    dirtyCollection(node);\n  } else {\n    if (value !== newValue) {\n      let oldKeysSize = 0;\n      let newKeysSize = 0;\n      let anyKeysAdded = false;\n      for (const _key in value) {\n        oldKeysSize++;\n      }\n      for (const key in newValue) {\n        newKeysSize++;\n        if (!(key in value)) {\n          anyKeysAdded = true;\n          break;\n        }\n      }\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n      if (isDifferent) {\n        dirtyCollection(node);\n      }\n    }\n  }\n  for (const key in tags) {\n    const childValue = value[key];\n    const newChildValue = newValue[key];\n    if (childValue !== newChildValue) {\n      dirtyCollection(node);\n      dirtyTag(tags[key], newChildValue);\n    }\n    if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      delete tags[key];\n    }\n  }\n  for (const key in children) {\n    const childNode = children[key];\n    const newChildValue = newValue[key];\n    const childValue = childNode.value;\n    if (childValue === newChildValue) {\n      continue;\n    } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      updateNode(childNode, newChildValue);\n    } else {\n      deleteNode(childNode);\n      delete children[key];\n    }\n  }\n}\nfunction deleteNode(node) {\n  if (node.tag) {\n    dirtyTag(node.tag, null);\n  }\n  dirtyCollection(node);\n  for (const key in node.tags) {\n    dirtyTag(node.tags[key], null);\n  }\n  for (const key in node.children) {\n    deleteNode(node.children[key]);\n  }\n}\n\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n  let entry;\n  return {\n    get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put(key, value) {\n      entry = { key, value };\n    },\n    getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear() {\n      entry = void 0;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  let entries = [];\n  function get(key) {\n    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));\n    if (cacheIndex > -1) {\n      const entry = entries[cacheIndex];\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    }\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      entries.unshift({ key, value });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return { get, put, getEntries, clear };\n}\nvar referenceEqualityCheck = (a, b) => a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    }\n    const { length } = prev;\n    for (let i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n  const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };\n  const {\n    equalityCheck = referenceEqualityCheck,\n    maxSize = 1,\n    resultEqualityCheck\n  } = providedOptions;\n  const comparator = createCacheKeyComparator(equalityCheck);\n  let resultsCount = 0;\n  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n  function memoized() {\n    let value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      value = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const entries = cache.getEntries();\n        const matchingEntry = entries.find(\n          (entry) => resultEqualityCheck(entry.value, value)\n        );\n        if (matchingEntry) {\n          value = matchingEntry.value;\n          resultsCount !== 0 && resultsCount--;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = () => {\n    cache.clear();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n  const node = createNode(\n    []\n  );\n  let lastArgs = null;\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n  const cache = createCache(() => {\n    const res = func.apply(null, node.proxy);\n    return res;\n  });\n  function memoized() {\n    if (!shallowEqual(lastArgs, arguments)) {\n      updateNode(node, arguments);\n      lastArgs = arguments;\n    }\n    return cache.value;\n  }\n  memoized.clearCache = () => {\n    return cache.clear();\n  };\n  return memoized;\n}\n\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n  constructor(value) {\n    this.value = value;\n  }\n  deref() {\n    return this.value;\n  }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n  return {\n    s: UNTERMINATED,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nfunction weakMapMemoize(func, options = {}) {\n  let fnNode = createCacheNode();\n  const { resultEqualityCheck } = options;\n  let lastResult;\n  let resultsCount = 0;\n  function memoized() {\n    let cacheNode = fnNode;\n    const { length } = arguments;\n    for (let i = 0, l = length; i < l; i++) {\n      const arg = arguments[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    const terminatedNode = cacheNode;\n    let result;\n    if (cacheNode.s === TERMINATED) {\n      result = cacheNode.v;\n    } else {\n      result = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const lastResultValue = lastResult?.deref?.() ?? lastResult;\n        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n          result = lastResultValue;\n          resultsCount !== 0 && resultsCount--;\n        }\n        const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n        lastResult = needsWeakRef ? new Ref(result) : result;\n      }\n    }\n    terminatedNode.s = TERMINATED;\n    terminatedNode.v = result;\n    return result;\n  }\n  memoized.clearCache = () => {\n    fnNode = createCacheNode();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n  const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n    memoize: memoizeOrOptions,\n    memoizeOptions: memoizeOptionsFromArgs\n  } : memoizeOrOptions;\n  const createSelector2 = (...createSelectorArgs) => {\n    let recomputations = 0;\n    let dependencyRecomputations = 0;\n    let lastResult;\n    let directlyPassedOptions = {};\n    let resultFunc = createSelectorArgs.pop();\n    if (typeof resultFunc === \"object\") {\n      directlyPassedOptions = resultFunc;\n      resultFunc = createSelectorArgs.pop();\n    }\n    assertIsFunction(\n      resultFunc,\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\n    );\n    const combinedOptions = {\n      ...createSelectorCreatorOptions,\n      ...directlyPassedOptions\n    };\n    const {\n      memoize,\n      memoizeOptions = [],\n      argsMemoize = weakMapMemoize,\n      argsMemoizeOptions = [],\n      devModeChecks = {}\n    } = combinedOptions;\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n    const dependencies = getDependencies(createSelectorArgs);\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\n      recomputations++;\n      return resultFunc.apply(\n        null,\n        arguments\n      );\n    }, ...finalMemoizeOptions);\n    let firstRun = true;\n    const selector = argsMemoize(function dependenciesChecker() {\n      dependencyRecomputations++;\n      const inputSelectorResults = collectInputSelectorResults(\n        dependencies,\n        arguments\n      );\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n      if (true) {\n        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n        if (identityFunctionCheck.shouldRun) {\n          identityFunctionCheck.run(\n            resultFunc,\n            inputSelectorResults,\n            lastResult\n          );\n        }\n        if (inputStabilityCheck.shouldRun) {\n          const inputSelectorResultsCopy = collectInputSelectorResults(\n            dependencies,\n            arguments\n          );\n          inputStabilityCheck.run(\n            { inputSelectorResults, inputSelectorResultsCopy },\n            { memoize, memoizeOptions: finalMemoizeOptions },\n            arguments\n          );\n        }\n        if (firstRun)\n          firstRun = false;\n      }\n      return lastResult;\n    }, ...finalArgsMemoizeOptions);\n    return Object.assign(selector, {\n      resultFunc,\n      memoizedResultFunc,\n      dependencies,\n      dependencyRecomputations: () => dependencyRecomputations,\n      resetDependencyRecomputations: () => {\n        dependencyRecomputations = 0;\n      },\n      lastResult: () => lastResult,\n      recomputations: () => recomputations,\n      resetRecomputations: () => {\n        recomputations = 0;\n      },\n      memoize,\n      argsMemoize\n    });\n  };\n  Object.assign(createSelector2, {\n    withTypes: () => createSelector2\n  });\n  return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign(\n  (inputSelectorsObject, selectorCreator = createSelector) => {\n    assertIsObject(\n      inputSelectorsObject,\n      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`\n    );\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map(\n      (key) => inputSelectorsObject[key]\n    );\n    const structuredSelector = selectorCreator(\n      dependencies,\n      (...inputSelectorResults) => {\n        return inputSelectorResults.reduce((composition, value, index) => {\n          composition[inputSelectorKeys[index]] = value;\n          return composition;\n        }, {});\n      }\n    );\n    return structuredSelector;\n  },\n  { withTypes: () => createStructuredSelector }\n);\n\n//# sourceMappingURL=reselect.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/reselect/dist/reselect.mjs\n");

/***/ })

};
;