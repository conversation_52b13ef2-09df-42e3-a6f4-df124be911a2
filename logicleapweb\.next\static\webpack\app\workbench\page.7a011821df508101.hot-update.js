"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./lib/api/course-management.ts":
/*!**************************************!*\
  !*** ./lib/api/course-management.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseManagementApi: function() { return /* binding */ courseManagementApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"(app-pages-browser)/./lib/api/request.ts\");\n\n// 开发配置\nconst DEV_CONFIG = {\n    // 设置为true可以在开发环境中强制发送真实请求（即使API不存在也会在Network中看到请求）\n    FORCE_REAL_REQUESTS: true,\n    // 设置为true可以看到详细的调试日志\n    ENABLE_DEBUG_LOGS: true\n};\n// 课程管理API类\nclass CourseManagementApi {\n    // 创建系列课程\n    async createSeriesCourse(data) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 开始创建系列课程\");\n            console.log(\"\\uD83D\\uDCDD 请求数据类型: JSON\");\n            console.log(\"\\uD83D\\uDCDD 请求数据:\", data);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series\");\n            console.log(\"⚙️ 环境:\", \"development\");\n        }\n        try {\n            // 只支持JSON格式\n            const config = {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发送请求配置:\", config);\n            // 发送真实请求（这样可以在Network面板中看到请求）\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.post(\"/api/v1/course-management/series\", data, config);\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到真实API响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 创建系列课程请求失败:\", error);\n                if (error.response) {\n                    console.error(\"❌ 错误状态:\", error.response.status);\n                    console.error(\"❌ 错误数据:\", error.response.data);\n                    console.error(\"❌ 错误头:\", error.response.headers);\n                }\n            }\n            // 在开发环境中不返回模拟数据，让错误正常抛出\n            throw error;\n        }\n    }\n    // 获取我的系列课程列表\n    async getMySeries(params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取我的系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/my-series\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/my-series\", {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            // 在开发环境中返回模拟数据\n            if (true) {\n                console.log(\"\\uD83C\\uDFAD 返回模拟系列课程数据\");\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                return {\n                    code: 200,\n                    message: \"success\",\n                    data: {\n                        list: [\n                            {\n                                id: 1,\n                                title: \"JavaScript高级编程\",\n                                description: \"深入学习JavaScript高级特性\",\n                                coverImage: \"https://via.placeholder.com/320x200/4F46E5/FFFFFF?text=JavaScript\",\n                                category: 0,\n                                categoryLabel: \"官方\",\n                                status: 0,\n                                statusLabel: \"草稿\",\n                                projectMembers: \"王老师、李助教\",\n                                totalCourses: 3,\n                                totalStudents: 0,\n                                contentSummary: {\n                                    videoCourseCount: 2,\n                                    documentCourseCount: 3,\n                                    totalResourcesCount: 8,\n                                    completionRate: 0.6\n                                },\n                                tags: [\n                                    {\n                                        id: 12,\n                                        name: \"编程\",\n                                        color: \"#007bff\",\n                                        category: 1,\n                                        categoryLabel: \"类型\"\n                                    },\n                                    {\n                                        id: 13,\n                                        name: \"入门\",\n                                        color: \"#28a745\",\n                                        category: 0,\n                                        categoryLabel: \"难度\"\n                                    }\n                                ],\n                                createdAt: \"2024-01-20T14:30:00Z\",\n                                updatedAt: \"2024-01-22T09:15:00Z\"\n                            }\n                        ],\n                        pagination: {\n                            page: 1,\n                            pageSize: 10,\n                            total: 1,\n                            totalPages: 1,\n                            hasNext: false,\n                            hasPrev: false\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    }\n    // 获取系列课程列表（保留原方法以兼容）\n    async getSeriesCourseList(params) {\n        return this.getMySeries({\n            page: params === null || params === void 0 ? void 0 : params.page,\n            pageSize: params === null || params === void 0 ? void 0 : params.size,\n            category: params === null || params === void 0 ? void 0 : params.category,\n            status: (params === null || params === void 0 ? void 0 : params.status) ? parseInt(params.status) : undefined\n        });\n    }\n    // 获取系列课程详情\n    async getSeriesCourseDetail(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"获取系列课程详情失败:\", error);\n            throw error;\n        }\n    }\n    // 更新系列课程\n    async updateSeriesCourse(id, data) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.put(\"/api/v1/course-management/series/\".concat(id), data);\n            return response.data;\n        } catch (error) {\n            console.error(\"更新系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 删除系列课程\n    async deleteSeriesCourse(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.delete(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"删除系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 获取系列下的课程列表\n    async getSeriesCourses(seriesId, params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series/\" + seriesId + \"/courses\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(seriesId, \"/courses\"), {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n}\n// 导出API实例\nconst courseManagementApi = new CourseManagementApi();\n/* harmony default export */ __webpack_exports__[\"default\"] = (courseManagementApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/course-management.ts\n"));

/***/ })

});