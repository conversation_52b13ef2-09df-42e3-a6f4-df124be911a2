"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-overflow";
exports.ids = ["vendor-chunks/rc-overflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-overflow/es/Item.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-overflow/es/Item.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n\n\n\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\n\n\n\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n  var mergedHidden = responsive && !display;\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(!invalidate && prefixCls, className),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalItem);\nItem.displayName = 'Item';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Item);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/Overflow.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-overflow/es/Overflow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\");\n/* harmony import */ var _RawItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RawItem */ \"(ssr)/./node_modules/rc-overflow/es/RawItem.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\n\n\n\n\n\n\n\n\n\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\n\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__.useBatcher)();\n  var _useEffectState = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, null),\n    _useEffectState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, new Map()),\n    _useEffectState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n  // ================================= Item =================================\n  var getKey = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n      key: key,\n      value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n  // >>>>> Rest node\n  var restNode;\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  if (!renderRawRest) {\n    var mergedRenderRest = renderRest || defaultRenderRest;\n    restNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  } else if (renderRawRest) {\n    restNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n      value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), restContextProps)\n    }, renderRawRest(omittedItems));\n  }\n  var overflowNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  if (isResponsive) {\n    overflowNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n      onResize: onOverflowResize,\n      disabled: !shouldResponsive\n    }, overflowNode);\n  }\n  return overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = _RawItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n// Convert to generic type\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardOverflow);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvT3ZlcmZsb3cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNXO0FBQ0M7QUFDb0I7QUFDMUY7QUFDK0I7QUFDd0I7QUFDbkI7QUFDWTtBQUNlO0FBQ3JDO0FBQzBDO0FBQ3BDO0FBQ1k7QUFDNUM7QUFDQTtBQUM0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOEZBQXdCO0FBQ3hDO0FBQ0EsMkJBQTJCLGlFQUFVO0FBQ3JDLHdCQUF3QixpRUFBYztBQUN0Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHVCQUF1QixvRkFBYztBQUNyQztBQUNBO0FBQ0EseUJBQXlCLGlFQUFjO0FBQ3ZDLHdCQUF3QixvRkFBYztBQUN0QztBQUNBO0FBQ0Esa0JBQWtCLCtDQUFRO0FBQzFCLGlCQUFpQixvRkFBYztBQUMvQjtBQUNBO0FBQ0EsbUJBQW1CLCtDQUFRO0FBQzNCLGlCQUFpQixvRkFBYztBQUMvQjtBQUNBO0FBQ0EsMkJBQTJCLDBDQUFhO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOENBQU87QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gscUJBQXFCLDhDQUFPO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsZUFBZSxrREFBVztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHlCQUF5QixrREFBVztBQUNwQztBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSw0RUFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsU0FBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnREFBbUIsQ0FBQyxzREFBZTtBQUMzRDtBQUNBLGFBQWEsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLHNCQUFzQjtBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJO0FBQ0o7QUFDQSx3QkFBd0IsZ0RBQW1CLENBQUMsNkNBQUksRUFBRSw4RUFBUSxHQUFHO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZ0RBQW1CLENBQUMsNkNBQUksRUFBRSw4RUFBUSxHQUFHO0FBQ2pFLElBQUk7QUFDSiw0QkFBNEIsZ0RBQW1CLENBQUMsc0RBQWU7QUFDL0QsYUFBYSxvRkFBYSxDQUFDLG9GQUFhLEdBQUc7QUFDM0MsS0FBSztBQUNMO0FBQ0Esa0NBQWtDLGdEQUFtQixZQUFZLDhFQUFRO0FBQ3pFLGVBQWUsaURBQVU7QUFDekI7QUFDQTtBQUNBLEdBQUcseUdBQXlHLGdEQUFtQixDQUFDLDZDQUFJLEVBQUUsOEVBQVEsR0FBRztBQUNqSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLGdDQUFnQyxnREFBbUIsQ0FBQywwREFBYztBQUNsRTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2Q0FBZ0I7QUFDbkQ7QUFDQSx1QkFBdUIsaURBQU87QUFDOUI7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsZUFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9PdmVyZmxvdy5qcz8xMjAzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJwcmVmaXhDbHNcIiwgXCJkYXRhXCIsIFwicmVuZGVySXRlbVwiLCBcInJlbmRlclJhd0l0ZW1cIiwgXCJpdGVtS2V5XCIsIFwiaXRlbVdpZHRoXCIsIFwic3NyXCIsIFwic3R5bGVcIiwgXCJjbGFzc05hbWVcIiwgXCJtYXhDb3VudFwiLCBcInJlbmRlclJlc3RcIiwgXCJyZW5kZXJSYXdSZXN0XCIsIFwic3VmZml4XCIsIFwiY29tcG9uZW50XCIsIFwiaXRlbUNvbXBvbmVudFwiLCBcIm9uVmlzaWJsZUNoYW5nZVwiXTtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VNZW1vLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IFJlc2l6ZU9ic2VydmVyIGZyb20gJ3JjLXJlc2l6ZS1vYnNlcnZlcic7XG5pbXBvcnQgdXNlTGF5b3V0RWZmZWN0IGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZUxheW91dEVmZmVjdFwiO1xuaW1wb3J0IEl0ZW0gZnJvbSAnLi9JdGVtJztcbmltcG9ydCB1c2VFZmZlY3RTdGF0ZSwgeyB1c2VCYXRjaGVyIH0gZnJvbSAnLi9ob29rcy91c2VFZmZlY3RTdGF0ZSc7XG5pbXBvcnQgUmF3SXRlbSBmcm9tICcuL1Jhd0l0ZW0nO1xuaW1wb3J0IHsgT3ZlcmZsb3dDb250ZXh0IH0gZnJvbSAnLi9jb250ZXh0JztcbnZhciBSRVNQT05TSVZFID0gJ3Jlc3BvbnNpdmUnO1xudmFyIElOVkFMSURBVEUgPSAnaW52YWxpZGF0ZSc7XG5leHBvcnQgeyBPdmVyZmxvd0NvbnRleHQgfSBmcm9tICcuL2NvbnRleHQnO1xuZnVuY3Rpb24gZGVmYXVsdFJlbmRlclJlc3Qob21pdHRlZEl0ZW1zKSB7XG4gIHJldHVybiBcIisgXCIuY29uY2F0KG9taXR0ZWRJdGVtcy5sZW5ndGgsIFwiIC4uLlwiKTtcbn1cbmZ1bmN0aW9uIE92ZXJmbG93KHByb3BzLCByZWYpIHtcbiAgdmFyIF9wcm9wcyRwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgcHJlZml4Q2xzID0gX3Byb3BzJHByZWZpeENscyA9PT0gdm9pZCAwID8gJ3JjLW92ZXJmbG93JyA6IF9wcm9wcyRwcmVmaXhDbHMsXG4gICAgX3Byb3BzJGRhdGEgPSBwcm9wcy5kYXRhLFxuICAgIGRhdGEgPSBfcHJvcHMkZGF0YSA9PT0gdm9pZCAwID8gW10gOiBfcHJvcHMkZGF0YSxcbiAgICByZW5kZXJJdGVtID0gcHJvcHMucmVuZGVySXRlbSxcbiAgICByZW5kZXJSYXdJdGVtID0gcHJvcHMucmVuZGVyUmF3SXRlbSxcbiAgICBpdGVtS2V5ID0gcHJvcHMuaXRlbUtleSxcbiAgICBfcHJvcHMkaXRlbVdpZHRoID0gcHJvcHMuaXRlbVdpZHRoLFxuICAgIGl0ZW1XaWR0aCA9IF9wcm9wcyRpdGVtV2lkdGggPT09IHZvaWQgMCA/IDEwIDogX3Byb3BzJGl0ZW1XaWR0aCxcbiAgICBzc3IgPSBwcm9wcy5zc3IsXG4gICAgc3R5bGUgPSBwcm9wcy5zdHlsZSxcbiAgICBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWUsXG4gICAgbWF4Q291bnQgPSBwcm9wcy5tYXhDb3VudCxcbiAgICByZW5kZXJSZXN0ID0gcHJvcHMucmVuZGVyUmVzdCxcbiAgICByZW5kZXJSYXdSZXN0ID0gcHJvcHMucmVuZGVyUmF3UmVzdCxcbiAgICBzdWZmaXggPSBwcm9wcy5zdWZmaXgsXG4gICAgX3Byb3BzJGNvbXBvbmVudCA9IHByb3BzLmNvbXBvbmVudCxcbiAgICBDb21wb25lbnQgPSBfcHJvcHMkY29tcG9uZW50ID09PSB2b2lkIDAgPyAnZGl2JyA6IF9wcm9wcyRjb21wb25lbnQsXG4gICAgaXRlbUNvbXBvbmVudCA9IHByb3BzLml0ZW1Db21wb25lbnQsXG4gICAgb25WaXNpYmxlQ2hhbmdlID0gcHJvcHMub25WaXNpYmxlQ2hhbmdlLFxuICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgdmFyIGZ1bGx5U1NSID0gc3NyID09PSAnZnVsbCc7XG4gIHZhciBub3RpZnlFZmZlY3RVcGRhdGUgPSB1c2VCYXRjaGVyKCk7XG4gIHZhciBfdXNlRWZmZWN0U3RhdGUgPSB1c2VFZmZlY3RTdGF0ZShub3RpZnlFZmZlY3RVcGRhdGUsIG51bGwpLFxuICAgIF91c2VFZmZlY3RTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlRWZmZWN0U3RhdGUsIDIpLFxuICAgIGNvbnRhaW5lcldpZHRoID0gX3VzZUVmZmVjdFN0YXRlMlswXSxcbiAgICBzZXRDb250YWluZXJXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZTJbMV07XG4gIHZhciBtZXJnZWRDb250YWluZXJXaWR0aCA9IGNvbnRhaW5lcldpZHRoIHx8IDA7XG4gIHZhciBfdXNlRWZmZWN0U3RhdGUzID0gdXNlRWZmZWN0U3RhdGUobm90aWZ5RWZmZWN0VXBkYXRlLCBuZXcgTWFwKCkpLFxuICAgIF91c2VFZmZlY3RTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlRWZmZWN0U3RhdGUzLCAyKSxcbiAgICBpdGVtV2lkdGhzID0gX3VzZUVmZmVjdFN0YXRlNFswXSxcbiAgICBzZXRJdGVtV2lkdGhzID0gX3VzZUVmZmVjdFN0YXRlNFsxXTtcbiAgdmFyIF91c2VFZmZlY3RTdGF0ZTUgPSB1c2VFZmZlY3RTdGF0ZShub3RpZnlFZmZlY3RVcGRhdGUsIDApLFxuICAgIF91c2VFZmZlY3RTdGF0ZTYgPSBfc2xpY2VkVG9BcnJheShfdXNlRWZmZWN0U3RhdGU1LCAyKSxcbiAgICBwcmV2UmVzdFdpZHRoID0gX3VzZUVmZmVjdFN0YXRlNlswXSxcbiAgICBzZXRQcmV2UmVzdFdpZHRoID0gX3VzZUVmZmVjdFN0YXRlNlsxXTtcbiAgdmFyIF91c2VFZmZlY3RTdGF0ZTcgPSB1c2VFZmZlY3RTdGF0ZShub3RpZnlFZmZlY3RVcGRhdGUsIDApLFxuICAgIF91c2VFZmZlY3RTdGF0ZTggPSBfc2xpY2VkVG9BcnJheShfdXNlRWZmZWN0U3RhdGU3LCAyKSxcbiAgICByZXN0V2lkdGggPSBfdXNlRWZmZWN0U3RhdGU4WzBdLFxuICAgIHNldFJlc3RXaWR0aCA9IF91c2VFZmZlY3RTdGF0ZThbMV07XG4gIHZhciBfdXNlRWZmZWN0U3RhdGU5ID0gdXNlRWZmZWN0U3RhdGUobm90aWZ5RWZmZWN0VXBkYXRlLCAwKSxcbiAgICBfdXNlRWZmZWN0U3RhdGUxMCA9IF9zbGljZWRUb0FycmF5KF91c2VFZmZlY3RTdGF0ZTksIDIpLFxuICAgIHN1ZmZpeFdpZHRoID0gX3VzZUVmZmVjdFN0YXRlMTBbMF0sXG4gICAgc2V0U3VmZml4V2lkdGggPSBfdXNlRWZmZWN0U3RhdGUxMFsxXTtcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKG51bGwpLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIHN1ZmZpeEZpeGVkU3RhcnQgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldFN1ZmZpeEZpeGVkU3RhcnQgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN0YXRlMyA9IHVzZVN0YXRlKG51bGwpLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBkaXNwbGF5Q291bnQgPSBfdXNlU3RhdGU0WzBdLFxuICAgIHNldERpc3BsYXlDb3VudCA9IF91c2VTdGF0ZTRbMV07XG4gIHZhciBtZXJnZWREaXNwbGF5Q291bnQgPSBSZWFjdC51c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoZGlzcGxheUNvdW50ID09PSBudWxsICYmIGZ1bGx5U1NSKSB7XG4gICAgICByZXR1cm4gTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVI7XG4gICAgfVxuICAgIHJldHVybiBkaXNwbGF5Q291bnQgfHwgMDtcbiAgfSwgW2Rpc3BsYXlDb3VudCwgY29udGFpbmVyV2lkdGhdKTtcbiAgdmFyIF91c2VTdGF0ZTUgPSB1c2VTdGF0ZShmYWxzZSksXG4gICAgX3VzZVN0YXRlNiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZTUsIDIpLFxuICAgIHJlc3RSZWFkeSA9IF91c2VTdGF0ZTZbMF0sXG4gICAgc2V0UmVzdFJlYWR5ID0gX3VzZVN0YXRlNlsxXTtcbiAgdmFyIGl0ZW1QcmVmaXhDbHMgPSBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWl0ZW1cIik7XG4gIC8vIEFsd2F5cyB1c2UgdGhlIG1heCB3aWR0aCB0byBhdm9pZCBibGlua1xuICB2YXIgbWVyZ2VkUmVzdFdpZHRoID0gTWF0aC5tYXgocHJldlJlc3RXaWR0aCwgcmVzdFdpZHRoKTtcbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IERhdGEgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBpc1Jlc3BvbnNpdmUgPSBtYXhDb3VudCA9PT0gUkVTUE9OU0lWRTtcbiAgdmFyIHNob3VsZFJlc3BvbnNpdmUgPSBkYXRhLmxlbmd0aCAmJiBpc1Jlc3BvbnNpdmU7XG4gIHZhciBpbnZhbGlkYXRlID0gbWF4Q291bnQgPT09IElOVkFMSURBVEU7XG4gIC8qKlxuICAgKiBXaGVuIGlzIGByZXNwb25zaXZlYCwgd2Ugd2lsbCBhbHdheXMgcmVuZGVyIHJlc3Qgbm9kZSB0byBnZXQgdGhlIHJlYWwgd2lkdGggb2YgaXQgZm9yIGNhbGN1bGF0aW9uXG4gICAqL1xuICB2YXIgc2hvd1Jlc3QgPSBzaG91bGRSZXNwb25zaXZlIHx8IHR5cGVvZiBtYXhDb3VudCA9PT0gJ251bWJlcicgJiYgZGF0YS5sZW5ndGggPiBtYXhDb3VudDtcbiAgdmFyIG1lcmdlZERhdGEgPSB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgaXRlbXMgPSBkYXRhO1xuICAgIGlmIChzaG91bGRSZXNwb25zaXZlKSB7XG4gICAgICBpZiAoY29udGFpbmVyV2lkdGggPT09IG51bGwgJiYgZnVsbHlTU1IpIHtcbiAgICAgICAgaXRlbXMgPSBkYXRhO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaXRlbXMgPSBkYXRhLnNsaWNlKDAsIE1hdGgubWluKGRhdGEubGVuZ3RoLCBtZXJnZWRDb250YWluZXJXaWR0aCAvIGl0ZW1XaWR0aCkpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodHlwZW9mIG1heENvdW50ID09PSAnbnVtYmVyJykge1xuICAgICAgaXRlbXMgPSBkYXRhLnNsaWNlKDAsIG1heENvdW50KTtcbiAgICB9XG4gICAgcmV0dXJuIGl0ZW1zO1xuICB9LCBbZGF0YSwgaXRlbVdpZHRoLCBjb250YWluZXJXaWR0aCwgbWF4Q291bnQsIHNob3VsZFJlc3BvbnNpdmVdKTtcbiAgdmFyIG9taXR0ZWRJdGVtcyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIGlmIChzaG91bGRSZXNwb25zaXZlKSB7XG4gICAgICByZXR1cm4gZGF0YS5zbGljZShtZXJnZWREaXNwbGF5Q291bnQgKyAxKTtcbiAgICB9XG4gICAgcmV0dXJuIGRhdGEuc2xpY2UobWVyZ2VkRGF0YS5sZW5ndGgpO1xuICB9LCBbZGF0YSwgbWVyZ2VkRGF0YSwgc2hvdWxkUmVzcG9uc2l2ZSwgbWVyZ2VkRGlzcGxheUNvdW50XSk7XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBJdGVtID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgZ2V0S2V5ID0gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7XG4gICAgdmFyIF9yZWY7XG4gICAgaWYgKHR5cGVvZiBpdGVtS2V5ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICByZXR1cm4gaXRlbUtleShpdGVtKTtcbiAgICB9XG4gICAgcmV0dXJuIChfcmVmID0gaXRlbUtleSAmJiAoaXRlbSA9PT0gbnVsbCB8fCBpdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiBpdGVtW2l0ZW1LZXldKSkgIT09IG51bGwgJiYgX3JlZiAhPT0gdm9pZCAwID8gX3JlZiA6IGluZGV4O1xuICB9LCBbaXRlbUtleV0pO1xuICB2YXIgbWVyZ2VkUmVuZGVySXRlbSA9IHVzZUNhbGxiYWNrKHJlbmRlckl0ZW0gfHwgZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICByZXR1cm4gaXRlbTtcbiAgfSwgW3JlbmRlckl0ZW1dKTtcbiAgZnVuY3Rpb24gdXBkYXRlRGlzcGxheUNvdW50KGNvdW50LCBzdWZmaXhGaXhlZFN0YXJ0VmFsLCBub3RSZWFkeSkge1xuICAgIC8vIFJlYWN0IDE4IHdpbGwgc3luYyByZW5kZXIgZXZlbiB3aGVuIHRoZSB2YWx1ZSBpcyBzYW1lIGluIHNvbWUgY2FzZS5cbiAgICAvLyBXZSB0YWtlIGBtZXJnZWREYXRhYCBhcyBkZXBzIHdoaWNoIG1heSBjYXVzZSBkZWFkIGxvb3AgaWYgaXQncyBkeW5hbWljIGdlbmVyYXRlLlxuICAgIC8vIHJlZjogaHR0cHM6Ly9naXRodWIuY29tL2FudC1kZXNpZ24vYW50LWRlc2lnbi9pc3N1ZXMvMzY1NTlcbiAgICBpZiAoZGlzcGxheUNvdW50ID09PSBjb3VudCAmJiAoc3VmZml4Rml4ZWRTdGFydFZhbCA9PT0gdW5kZWZpbmVkIHx8IHN1ZmZpeEZpeGVkU3RhcnRWYWwgPT09IHN1ZmZpeEZpeGVkU3RhcnQpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHNldERpc3BsYXlDb3VudChjb3VudCk7XG4gICAgaWYgKCFub3RSZWFkeSkge1xuICAgICAgc2V0UmVzdFJlYWR5KGNvdW50IDwgZGF0YS5sZW5ndGggLSAxKTtcbiAgICAgIG9uVmlzaWJsZUNoYW5nZSA9PT0gbnVsbCB8fCBvblZpc2libGVDaGFuZ2UgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9uVmlzaWJsZUNoYW5nZShjb3VudCk7XG4gICAgfVxuICAgIGlmIChzdWZmaXhGaXhlZFN0YXJ0VmFsICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHNldFN1ZmZpeEZpeGVkU3RhcnQoc3VmZml4Rml4ZWRTdGFydFZhbCk7XG4gICAgfVxuICB9XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBTaXplID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBmdW5jdGlvbiBvbk92ZXJmbG93UmVzaXplKF8sIGVsZW1lbnQpIHtcbiAgICBzZXRDb250YWluZXJXaWR0aChlbGVtZW50LmNsaWVudFdpZHRoKTtcbiAgfVxuICBmdW5jdGlvbiByZWdpc3RlclNpemUoa2V5LCB3aWR0aCkge1xuICAgIHNldEl0ZW1XaWR0aHMoZnVuY3Rpb24gKG9yaWdpbikge1xuICAgICAgdmFyIGNsb25lID0gbmV3IE1hcChvcmlnaW4pO1xuICAgICAgaWYgKHdpZHRoID09PSBudWxsKSB7XG4gICAgICAgIGNsb25lLmRlbGV0ZShrZXkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY2xvbmUuc2V0KGtleSwgd2lkdGgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGNsb25lO1xuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIHJlZ2lzdGVyT3ZlcmZsb3dTaXplKF8sIHdpZHRoKSB7XG4gICAgc2V0UmVzdFdpZHRoKHdpZHRoKTtcbiAgICBzZXRQcmV2UmVzdFdpZHRoKHJlc3RXaWR0aCk7XG4gIH1cbiAgZnVuY3Rpb24gcmVnaXN0ZXJTdWZmaXhTaXplKF8sIHdpZHRoKSB7XG4gICAgc2V0U3VmZml4V2lkdGgod2lkdGgpO1xuICB9XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IEVmZmVjdCA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICBmdW5jdGlvbiBnZXRJdGVtV2lkdGgoaW5kZXgpIHtcbiAgICByZXR1cm4gaXRlbVdpZHRocy5nZXQoZ2V0S2V5KG1lcmdlZERhdGFbaW5kZXhdLCBpbmRleCkpO1xuICB9XG4gIHVzZUxheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKG1lcmdlZENvbnRhaW5lcldpZHRoICYmIHR5cGVvZiBtZXJnZWRSZXN0V2lkdGggPT09ICdudW1iZXInICYmIG1lcmdlZERhdGEpIHtcbiAgICAgIHZhciB0b3RhbFdpZHRoID0gc3VmZml4V2lkdGg7XG4gICAgICB2YXIgbGVuID0gbWVyZ2VkRGF0YS5sZW5ndGg7XG4gICAgICB2YXIgbGFzdEluZGV4ID0gbGVuIC0gMTtcbiAgICAgIC8vIFdoZW4gZGF0YSBjb3VudCBjaGFuZ2UgdG8gMCwgcmVzZXQgdGhpcyBzaW5jZSBub3QgbG9vcCB3aWxsIHJlYWNoXG4gICAgICBpZiAoIWxlbikge1xuICAgICAgICB1cGRhdGVEaXNwbGF5Q291bnQoMCwgbnVsbCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuOyBpICs9IDEpIHtcbiAgICAgICAgdmFyIGN1cnJlbnRJdGVtV2lkdGggPSBnZXRJdGVtV2lkdGgoaSk7XG4gICAgICAgIC8vIEZ1bGx5IHdpbGwgYWx3YXlzIHJlbmRlclxuICAgICAgICBpZiAoZnVsbHlTU1IpIHtcbiAgICAgICAgICBjdXJyZW50SXRlbVdpZHRoID0gY3VycmVudEl0ZW1XaWR0aCB8fCAwO1xuICAgICAgICB9XG4gICAgICAgIC8vIEJyZWFrIHNpbmNlIGRhdGEgbm90IHJlYWR5XG4gICAgICAgIGlmIChjdXJyZW50SXRlbVdpZHRoID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICB1cGRhdGVEaXNwbGF5Q291bnQoaSAtIDEsIHVuZGVmaW5lZCwgdHJ1ZSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgLy8gRmluZCBiZXN0IG1hdGNoXG4gICAgICAgIHRvdGFsV2lkdGggKz0gY3VycmVudEl0ZW1XaWR0aDtcbiAgICAgICAgaWYgKFxuICAgICAgICAvLyBPbmx5IG9uZSBtZWFucyBgdG90YWxXaWR0aGAgaXMgdGhlIGZpbmFsIHdpZHRoXG4gICAgICAgIGxhc3RJbmRleCA9PT0gMCAmJiB0b3RhbFdpZHRoIDw9IG1lcmdlZENvbnRhaW5lcldpZHRoIHx8XG4gICAgICAgIC8vIExhc3QgdHdvIHdpZHRoIHdpbGwgYmUgdGhlIGZpbmFsIHdpZHRoXG4gICAgICAgIGkgPT09IGxhc3RJbmRleCAtIDEgJiYgdG90YWxXaWR0aCArIGdldEl0ZW1XaWR0aChsYXN0SW5kZXgpIDw9IG1lcmdlZENvbnRhaW5lcldpZHRoKSB7XG4gICAgICAgICAgLy8gQWRkaXRpb25hbCBjaGVjayBpZiBtYXRjaCB0aGUgZW5kXG4gICAgICAgICAgdXBkYXRlRGlzcGxheUNvdW50KGxhc3RJbmRleCwgbnVsbCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH0gZWxzZSBpZiAodG90YWxXaWR0aCArIG1lcmdlZFJlc3RXaWR0aCA+IG1lcmdlZENvbnRhaW5lcldpZHRoKSB7XG4gICAgICAgICAgLy8gQ2FuIG5vdCBob2xkIGFsbCB0aGUgY29udGVudCB0byBzaG93IHJlc3RcbiAgICAgICAgICB1cGRhdGVEaXNwbGF5Q291bnQoaSAtIDEsIHRvdGFsV2lkdGggLSBjdXJyZW50SXRlbVdpZHRoIC0gc3VmZml4V2lkdGggKyByZXN0V2lkdGgpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoc3VmZml4ICYmIGdldEl0ZW1XaWR0aCgwKSArIHN1ZmZpeFdpZHRoID4gbWVyZ2VkQ29udGFpbmVyV2lkdGgpIHtcbiAgICAgICAgc2V0U3VmZml4Rml4ZWRTdGFydChudWxsKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFttZXJnZWRDb250YWluZXJXaWR0aCwgaXRlbVdpZHRocywgcmVzdFdpZHRoLCBzdWZmaXhXaWR0aCwgZ2V0S2V5LCBtZXJnZWREYXRhXSk7XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IFJlbmRlciA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgZGlzcGxheVJlc3QgPSByZXN0UmVhZHkgJiYgISFvbWl0dGVkSXRlbXMubGVuZ3RoO1xuICB2YXIgc3VmZml4U3R5bGUgPSB7fTtcbiAgaWYgKHN1ZmZpeEZpeGVkU3RhcnQgIT09IG51bGwgJiYgc2hvdWxkUmVzcG9uc2l2ZSkge1xuICAgIHN1ZmZpeFN0eWxlID0ge1xuICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICBsZWZ0OiBzdWZmaXhGaXhlZFN0YXJ0LFxuICAgICAgdG9wOiAwXG4gICAgfTtcbiAgfVxuICB2YXIgaXRlbVNoYXJlZFByb3BzID0ge1xuICAgIHByZWZpeENsczogaXRlbVByZWZpeENscyxcbiAgICByZXNwb25zaXZlOiBzaG91bGRSZXNwb25zaXZlLFxuICAgIGNvbXBvbmVudDogaXRlbUNvbXBvbmVudCxcbiAgICBpbnZhbGlkYXRlOiBpbnZhbGlkYXRlXG4gIH07XG4gIC8vID4+Pj4+IENob2ljZSByZW5kZXIgZnVuIGJ5IGByZW5kZXJSYXdJdGVtYFxuICB2YXIgaW50ZXJuYWxSZW5kZXJJdGVtTm9kZSA9IHJlbmRlclJhd0l0ZW0gPyBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHtcbiAgICB2YXIga2V5ID0gZ2V0S2V5KGl0ZW0sIGluZGV4KTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoT3ZlcmZsb3dDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgICBrZXk6IGtleSxcbiAgICAgIHZhbHVlOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGl0ZW1TaGFyZWRQcm9wcyksIHt9LCB7XG4gICAgICAgIG9yZGVyOiBpbmRleCxcbiAgICAgICAgaXRlbTogaXRlbSxcbiAgICAgICAgaXRlbUtleToga2V5LFxuICAgICAgICByZWdpc3RlclNpemU6IHJlZ2lzdGVyU2l6ZSxcbiAgICAgICAgZGlzcGxheTogaW5kZXggPD0gbWVyZ2VkRGlzcGxheUNvdW50XG4gICAgICB9KVxuICAgIH0sIHJlbmRlclJhd0l0ZW0oaXRlbSwgaW5kZXgpKTtcbiAgfSA6IGZ1bmN0aW9uIChpdGVtLCBpbmRleCkge1xuICAgIHZhciBrZXkgPSBnZXRLZXkoaXRlbSwgaW5kZXgpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChJdGVtLCBfZXh0ZW5kcyh7fSwgaXRlbVNoYXJlZFByb3BzLCB7XG4gICAgICBvcmRlcjogaW5kZXgsXG4gICAgICBrZXk6IGtleSxcbiAgICAgIGl0ZW06IGl0ZW0sXG4gICAgICByZW5kZXJJdGVtOiBtZXJnZWRSZW5kZXJJdGVtLFxuICAgICAgaXRlbUtleToga2V5LFxuICAgICAgcmVnaXN0ZXJTaXplOiByZWdpc3RlclNpemUsXG4gICAgICBkaXNwbGF5OiBpbmRleCA8PSBtZXJnZWREaXNwbGF5Q291bnRcbiAgICB9KSk7XG4gIH07XG4gIC8vID4+Pj4+IFJlc3Qgbm9kZVxuICB2YXIgcmVzdE5vZGU7XG4gIHZhciByZXN0Q29udGV4dFByb3BzID0ge1xuICAgIG9yZGVyOiBkaXNwbGF5UmVzdCA/IG1lcmdlZERpc3BsYXlDb3VudCA6IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSLFxuICAgIGNsYXNzTmFtZTogXCJcIi5jb25jYXQoaXRlbVByZWZpeENscywgXCItcmVzdFwiKSxcbiAgICByZWdpc3RlclNpemU6IHJlZ2lzdGVyT3ZlcmZsb3dTaXplLFxuICAgIGRpc3BsYXk6IGRpc3BsYXlSZXN0XG4gIH07XG4gIGlmICghcmVuZGVyUmF3UmVzdCkge1xuICAgIHZhciBtZXJnZWRSZW5kZXJSZXN0ID0gcmVuZGVyUmVzdCB8fCBkZWZhdWx0UmVuZGVyUmVzdDtcbiAgICByZXN0Tm9kZSA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEl0ZW0sIF9leHRlbmRzKHt9LCBpdGVtU2hhcmVkUHJvcHMsIHJlc3RDb250ZXh0UHJvcHMpLCB0eXBlb2YgbWVyZ2VkUmVuZGVyUmVzdCA9PT0gJ2Z1bmN0aW9uJyA/IG1lcmdlZFJlbmRlclJlc3Qob21pdHRlZEl0ZW1zKSA6IG1lcmdlZFJlbmRlclJlc3QpO1xuICB9IGVsc2UgaWYgKHJlbmRlclJhd1Jlc3QpIHtcbiAgICByZXN0Tm9kZSA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE92ZXJmbG93Q29udGV4dC5Qcm92aWRlciwge1xuICAgICAgdmFsdWU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgaXRlbVNoYXJlZFByb3BzKSwgcmVzdENvbnRleHRQcm9wcylcbiAgICB9LCByZW5kZXJSYXdSZXN0KG9taXR0ZWRJdGVtcykpO1xuICB9XG4gIHZhciBvdmVyZmxvd05vZGUgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDb21wb25lbnQsIF9leHRlbmRzKHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMoIWludmFsaWRhdGUgJiYgcHJlZml4Q2xzLCBjbGFzc05hbWUpLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICByZWY6IHJlZlxuICB9LCByZXN0UHJvcHMpLCBtZXJnZWREYXRhLm1hcChpbnRlcm5hbFJlbmRlckl0ZW1Ob2RlKSwgc2hvd1Jlc3QgPyByZXN0Tm9kZSA6IG51bGwsIHN1ZmZpeCAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChJdGVtLCBfZXh0ZW5kcyh7fSwgaXRlbVNoYXJlZFByb3BzLCB7XG4gICAgcmVzcG9uc2l2ZTogaXNSZXNwb25zaXZlLFxuICAgIHJlc3BvbnNpdmVEaXNhYmxlZDogIXNob3VsZFJlc3BvbnNpdmUsXG4gICAgb3JkZXI6IG1lcmdlZERpc3BsYXlDb3VudCxcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KGl0ZW1QcmVmaXhDbHMsIFwiLXN1ZmZpeFwiKSxcbiAgICByZWdpc3RlclNpemU6IHJlZ2lzdGVyU3VmZml4U2l6ZSxcbiAgICBkaXNwbGF5OiB0cnVlLFxuICAgIHN0eWxlOiBzdWZmaXhTdHlsZVxuICB9KSwgc3VmZml4KSk7XG4gIGlmIChpc1Jlc3BvbnNpdmUpIHtcbiAgICBvdmVyZmxvd05vZGUgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZXNpemVPYnNlcnZlciwge1xuICAgICAgb25SZXNpemU6IG9uT3ZlcmZsb3dSZXNpemUsXG4gICAgICBkaXNhYmxlZDogIXNob3VsZFJlc3BvbnNpdmVcbiAgICB9LCBvdmVyZmxvd05vZGUpO1xuICB9XG4gIHJldHVybiBvdmVyZmxvd05vZGU7XG59XG52YXIgRm9yd2FyZE92ZXJmbG93ID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoT3ZlcmZsb3cpO1xuRm9yd2FyZE92ZXJmbG93LmRpc3BsYXlOYW1lID0gJ092ZXJmbG93JztcbkZvcndhcmRPdmVyZmxvdy5JdGVtID0gUmF3SXRlbTtcbkZvcndhcmRPdmVyZmxvdy5SRVNQT05TSVZFID0gUkVTUE9OU0lWRTtcbkZvcndhcmRPdmVyZmxvdy5JTlZBTElEQVRFID0gSU5WQUxJREFURTtcbi8vIENvbnZlcnQgdG8gZ2VuZXJpYyB0eXBlXG5leHBvcnQgZGVmYXVsdCBGb3J3YXJkT3ZlcmZsb3c7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Overflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/RawItem.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/RawItem.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\n\n\n\n\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext);\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(context, _excluded2);\n  var className = props.className,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded3);\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Item__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RawItem);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/RawItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/context.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* binding */ OverflowContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OverflowContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEI7QUFDbkIsbUNBQW1DLDBEQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9jb250ZXh0LmpzP2Q0NjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgT3ZlcmZsb3dDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/channelUpdate.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ channelUpdate)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n\nfunction channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvY2hhbm5lbFVwZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsSUFBSSwwREFBRztBQUNQLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9ob29rcy9jaGFubmVsVXBkYXRlLmpzPzIwODIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJhZiBmcm9tIFwicmMtdXRpbC9lcy9yYWZcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNoYW5uZWxVcGRhdGUoY2FsbGJhY2spIHtcbiAgaWYgKHR5cGVvZiBNZXNzYWdlQ2hhbm5lbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByYWYoY2FsbGJhY2spO1xuICB9IGVsc2Uge1xuICAgIHZhciBjaGFubmVsID0gbmV3IE1lc3NhZ2VDaGFubmVsKCk7XG4gICAgY2hhbm5lbC5wb3J0MS5vbm1lc3NhZ2UgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICB9O1xuICAgIGNoYW5uZWwucG9ydDIucG9zdE1lc3NhZ2UodW5kZWZpbmVkKTtcbiAgfVxufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState),\n/* harmony export */   useBatcher: () => (/* binding */ useBatcher)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _channelUpdate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./channelUpdate */ \"(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\");\n\n\n\n\n\n/**\n * Batcher for record any `useEffectState` need update.\n */\nfunction useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      (0,_channelUpdate__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_batchedUpdates)(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nfunction useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(defaultValue),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n  // Set State\n  var setEffectVal = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvdXNlRWZmZWN0U3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXNFO0FBQ3JCO0FBQ2xCO0FBQ3FCO0FBQ1I7QUFDNUM7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMERBQWE7QUFDbkIsUUFBUSxrRUFBdUI7QUFDL0I7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2Y7QUFDQSx3QkFBd0IsMkNBQWM7QUFDdEMsdUJBQXVCLG9GQUFjO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixxRUFBUTtBQUM3QjtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW92ZXJmbG93L2VzL2hvb2tzL3VzZUVmZmVjdFN0YXRlLmpzPzkwZjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgdXNlRXZlbnQgZnJvbSBcInJjLXV0aWwvZXMvaG9va3MvdXNlRXZlbnRcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzIH0gZnJvbSAncmVhY3QtZG9tJztcbmltcG9ydCBjaGFubmVsVXBkYXRlIGZyb20gJy4vY2hhbm5lbFVwZGF0ZSc7XG4vKipcbiAqIEJhdGNoZXIgZm9yIHJlY29yZCBhbnkgYHVzZUVmZmVjdFN0YXRlYCBuZWVkIHVwZGF0ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUJhdGNoZXIoKSB7XG4gIC8vIFVwZGF0ZXIgVHJpZ2dlclxuICB2YXIgdXBkYXRlRnVuY1JlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgLy8gTm90aWZ5IHVwZGF0ZVxuICB2YXIgbm90aWZ5RWZmZWN0VXBkYXRlID0gZnVuY3Rpb24gbm90aWZ5RWZmZWN0VXBkYXRlKGNhbGxiYWNrKSB7XG4gICAgaWYgKCF1cGRhdGVGdW5jUmVmLmN1cnJlbnQpIHtcbiAgICAgIHVwZGF0ZUZ1bmNSZWYuY3VycmVudCA9IFtdO1xuICAgICAgY2hhbm5lbFVwZGF0ZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICB1cGRhdGVGdW5jUmVmLmN1cnJlbnQuZm9yRWFjaChmdW5jdGlvbiAoZm4pIHtcbiAgICAgICAgICAgIGZuKCk7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgdXBkYXRlRnVuY1JlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9XG4gICAgdXBkYXRlRnVuY1JlZi5jdXJyZW50LnB1c2goY2FsbGJhY2spO1xuICB9O1xuICByZXR1cm4gbm90aWZ5RWZmZWN0VXBkYXRlO1xufVxuLyoqXG4gKiBUcmlnZ2VyIHN0YXRlIHVwZGF0ZSBieSBgdXNlTGF5b3V0RWZmZWN0YCB0byBzYXZlIHBlcmYuXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUVmZmVjdFN0YXRlKG5vdGlmeUVmZmVjdFVwZGF0ZSwgZGVmYXVsdFZhbHVlKSB7XG4gIC8vIFZhbHVlXG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZShkZWZhdWx0VmFsdWUpLFxuICAgIF9SZWFjdCR1c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfUmVhY3QkdXNlU3RhdGUsIDIpLFxuICAgIHN0YXRlVmFsdWUgPSBfUmVhY3QkdXNlU3RhdGUyWzBdLFxuICAgIHNldFN0YXRlVmFsdWUgPSBfUmVhY3QkdXNlU3RhdGUyWzFdO1xuICAvLyBTZXQgU3RhdGVcbiAgdmFyIHNldEVmZmVjdFZhbCA9IHVzZUV2ZW50KGZ1bmN0aW9uIChuZXh0VmFsdWUpIHtcbiAgICBub3RpZnlFZmZlY3RVcGRhdGUoZnVuY3Rpb24gKCkge1xuICAgICAgc2V0U3RhdGVWYWx1ZShuZXh0VmFsdWUpO1xuICAgIH0pO1xuICB9KTtcbiAgcmV0dXJuIFtzdGF0ZVZhbHVlLCBzZXRFZmZlY3RWYWxdO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-overflow/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Overflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Overflow */ \"(ssr)/./node_modules/rc-overflow/es/Overflow.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Overflow__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanM/ZDIxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgT3ZlcmZsb3cgZnJvbSAnLi9PdmVyZmxvdyc7XG5leHBvcnQgZGVmYXVsdCBPdmVyZmxvdzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/index.js\n");

/***/ })

};
;