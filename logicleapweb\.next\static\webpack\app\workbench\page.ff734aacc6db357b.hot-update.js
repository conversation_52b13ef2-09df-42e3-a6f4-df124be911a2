"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./lib/api/course-management.ts":
/*!**************************************!*\
  !*** ./lib/api/course-management.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseManagementApi: function() { return /* binding */ courseManagementApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"(app-pages-browser)/./lib/api/request.ts\");\n\n// 开发配置\nconst DEV_CONFIG = {\n    // 设置为true可以在开发环境中强制发送真实请求（即使API不存在也会在Network中看到请求）\n    FORCE_REAL_REQUESTS: true,\n    // 设置为true可以看到详细的调试日志\n    ENABLE_DEBUG_LOGS: true\n};\n// 课程管理API类\nclass CourseManagementApi {\n    // 创建系列课程\n    async createSeriesCourse(data) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 开始创建系列课程\");\n            console.log(\"\\uD83D\\uDCDD 请求数据类型: JSON\");\n            console.log(\"\\uD83D\\uDCDD 请求数据:\", data);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series\");\n            console.log(\"⚙️ 环境:\", \"development\");\n        }\n        try {\n            // 只支持JSON格式\n            const config = {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发送请求配置:\", config);\n            // 发送真实请求（这样可以在Network面板中看到请求）\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.post(\"/api/v1/course-management/series\", data, config);\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到真实API响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 创建系列课程请求失败:\", error);\n                if (error.response) {\n                    console.error(\"❌ 错误状态:\", error.response.status);\n                    console.error(\"❌ 错误数据:\", error.response.data);\n                    console.error(\"❌ 错误头:\", error.response.headers);\n                }\n            }\n            // 在开发环境中不返回模拟数据，让错误正常抛出\n            throw error;\n        }\n    }\n    // 获取我的系列课程列表\n    async getMySeries(params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取我的系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/my-series\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/my-series\", {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            // 在开发环境中返回模拟数据\n            if (true) {\n                console.log(\"\\uD83C\\uDFAD 返回模拟系列课程数据\");\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                return {\n                    code: 200,\n                    message: \"success\",\n                    data: {\n                        list: [\n                            {\n                                id: 1,\n                                title: \"JavaScript高级编程\",\n                                description: \"深入学习JavaScript高级特性\",\n                                coverImage: \"https://via.placeholder.com/320x200/4F46E5/FFFFFF?text=JavaScript\",\n                                category: 0,\n                                categoryLabel: \"官方\",\n                                status: 0,\n                                statusLabel: \"草稿\",\n                                projectMembers: \"王老师、李助教\",\n                                totalCourses: 3,\n                                totalStudents: 0,\n                                contentSummary: {\n                                    videoCourseCount: 2,\n                                    documentCourseCount: 3,\n                                    totalResourcesCount: 8,\n                                    completionRate: 0.6\n                                },\n                                createdAt: \"2024-01-20T14:30:00Z\",\n                                updatedAt: \"2024-01-22T09:15:00Z\"\n                            }\n                        ],\n                        pagination: {\n                            page: 1,\n                            pageSize: 10,\n                            total: 1,\n                            totalPages: 1,\n                            hasNext: false,\n                            hasPrev: false\n                        }\n                    }\n                };\n            }\n            throw error;\n        }\n    }\n    // 获取系列课程列表（保留原方法以兼容）\n    async getSeriesCourseList(params) {\n        return this.getMySeries({\n            page: params === null || params === void 0 ? void 0 : params.page,\n            pageSize: params === null || params === void 0 ? void 0 : params.size,\n            category: params === null || params === void 0 ? void 0 : params.category,\n            status: (params === null || params === void 0 ? void 0 : params.status) ? parseInt(params.status) : undefined\n        });\n    }\n    // 获取系列课程详情\n    async getSeriesCourseDetail(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"获取系列课程详情失败:\", error);\n            throw error;\n        }\n    }\n    // 更新系列课程\n    async updateSeriesCourse(id, data) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.put(\"/api/v1/course-management/series/\".concat(id), data);\n            return response.data;\n        } catch (error) {\n            console.error(\"更新系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 删除系列课程\n    async deleteSeriesCourse(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.delete(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"删除系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 获取系列下的课程列表\n    async getSeriesCourses(seriesId, params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series/\" + seriesId + \"/courses\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(seriesId, \"/courses\"), {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n}\n// 导出API实例\nconst courseManagementApi = new CourseManagementApi();\n/* harmony default export */ __webpack_exports__[\"default\"] = (courseManagementApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/course-management.ts\n"));

/***/ })

});