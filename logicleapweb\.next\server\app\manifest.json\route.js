"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.json/route";
exports.ids = ["app/manifest.json/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=D%3A%5Clogicleap%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clogicleap%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=D%3A%5Clogicleap%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clogicleap%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_D_3A_5Clogicleap_5Clogicleapweb_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.json/route\",\n        pathname: \"/manifest.json\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.json/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_D_3A_5Clogicleap_5Clogicleapweb_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/manifest.json/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=D%3A%5Clogicleap%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clogicleap%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"application/manifest+json\"\nconst buffer = Buffer.from(\"ew0KICAic2hvcnRfbmFtZSI6ICJSZWFjdCBBcHAiLA0KICAibmFtZSI6ICJDcmVhdGUgUmVhY3QgQXBwIFNhbXBsZSIsDQogICJpY29ucyI6IFsNCiAgICB7DQogICAgICAic3JjIjogImZhdmljb24uaWNvIiwNCiAgICAgICJzaXplcyI6ICI2NHg2NCAzMngzMiAyNHgyNCAxNngxNiIsDQogICAgICAidHlwZSI6ICJpbWFnZS94LWljb24iDQogICAgfSwNCiAgICB7DQogICAgICAic3JjIjogImxvZ28xOTIucG5nIiwNCiAgICAgICJ0eXBlIjogImltYWdlL3BuZyIsDQogICAgICAic2l6ZXMiOiAiMTkyeDE5MiINCiAgICB9LA0KICAgIHsNCiAgICAgICJzcmMiOiAibG9nbzUxMi5wbmciLA0KICAgICAgInR5cGUiOiAiaW1hZ2UvcG5nIiwNCiAgICAgICJzaXplcyI6ICI1MTJ4NTEyIg0KICAgIH0NCiAgXSwNCiAgInN0YXJ0X3VybCI6ICIuIiwNCiAgImRpc3BsYXkiOiAic3RhbmRhbG9uZSIsDQogICJ0aGVtZV9jb2xvciI6ICIjMDAwMDAwIiwNCiAgImJhY2tncm91bmRfY29sb3IiOiAiI2ZmZmZmZiINCn0NCg==\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"no-cache, no-store\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=D%3A%5Clogicleap%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=D%3A%5Clogicleap%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Clogicleap%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();