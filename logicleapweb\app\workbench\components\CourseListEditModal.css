/* 课程列表编辑弹窗样式 */
.course-list-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.course-list-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1200px;
  height: 85vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* 头部样式 */
.course-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.course-list-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-list-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.course-list-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.course-list-settings-btn,
.course-list-add-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-settings-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.course-list-settings-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.course-list-add-btn {
  background: #3b82f6;
  color: white;
}

.course-list-add-btn:hover {
  background: #2563eb;
}

.course-list-close-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 主要内容区域 */
.course-list-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧课程列表 */
.course-list-sidebar {
  width: 280px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
}

.course-list-items {
  padding: 16px;
}

.course-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.course-list-item-text {
  font-size: 14px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-list-item-delete {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.course-list-item:hover .course-list-item-delete {
  opacity: 1;
}

.course-list-item-delete:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 右侧编辑区域 */
.course-list-edit-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 系列课程封面 */
.course-series-cover {
  width: 200px;
  height: 120px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.course-series-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-series-cover-placeholder {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
}

/* 编辑表单 */
.course-edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.course-edit-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.course-edit-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.course-edit-input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  transition: border-color 0.2s ease;
}

.course-edit-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.course-edit-input-small {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  transition: border-color 0.2s ease;
}

.course-edit-input-small:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-add-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-edit-add-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 底部按钮 */
.course-list-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.course-list-footer-left {
  display: flex;
  align-items: center;
}

.course-list-footer-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-list-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.course-list-btn-publish {
  background: #10b981;
  color: white;
}

.course-list-btn-publish:hover {
  background: #059669;
}

.course-list-btn-publish {
  background: #10b981;
  color: white;
}

.course-list-btn-publish:hover {
  background: #059669;
}

.course-list-btn-exit {
  background: #6b7280;
  color: white;
}

.course-list-btn-exit:hover {
  background: #4b5563;
}

.course-list-btn-save {
  background: #3b82f6;
  color: white;
}

.course-list-btn-save:hover {
  background: #2563eb;
}

/* 设置按钮激活状态 */
.course-list-settings-btn.active {
  background: #3b82f6;
  color: white;
}

.course-list-settings-btn.active:hover {
  background: #2563eb;
}

/* 课程项目激活状态 */
.course-list-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 左侧课程列表空状态 */
.course-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  height: 100%;
  min-height: 300px;
}

.course-list-empty-icon {
  margin-bottom: 16px;
}

.course-list-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.course-list-empty-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.course-list-empty-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-empty-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 加载状态 */
.course-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* 右侧编辑区域空状态 */
.course-edit-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
}

.course-edit-empty-icon {
  margin-bottom: 20px;
}

.course-edit-empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.course-edit-empty-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 新增表单元素样式 */
.course-edit-textarea {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.course-edit-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.course-edit-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-unit {
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
  white-space: nowrap;
}

/* 课程状态标签 */
.course-status-label {
  display: inline-block;
  padding: 4px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* 视频时长显示 */
.course-video-duration {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

/* 课程详细编辑界面 */
.course-detail-edit {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

/* 顶部区域 */
.course-detail-top {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.course-detail-cover {
  flex-shrink: 0;
  width: 120px;
  height: 80px;
}

.course-cover-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}

.course-detail-basic {
  flex: 1;
}

.course-detail-field {
  margin-bottom: 12px;
}

.course-detail-field label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
}

.course-detail-field input,
.course-detail-field textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.course-detail-field input:focus,
.course-detail-field textarea:focus {
  outline: none;
  border-color: #1890ff;
}

/* 课程详细区域 */
.course-detail-section {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.course-detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 课程资源 */
.course-resource-item {
  margin-bottom: 16px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.resource-header span {
  font-size: 14px;
  color: #333;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 视频上传区域 */
.video-upload-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

.video-placeholder {
  width: 120px;
  height: 80px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
}

.play-icon {
  font-size: 24px;
  color: #666;
}

.upload-btn {
  padding: 6px 12px;
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.upload-btn:hover {
  background-color: #e6e6e6;
}

/* 附件区域 */
.attachment-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

.attachment-area input {
  flex: 1;
}

/* 教学附件 */
.teaching-materials {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.add-material-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: none;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.add-material-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.material-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 10px;
  text-align: center;
  padding: 4px;
  line-height: 1.2;
}

/* 课程内容区域 */
.course-content-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.content-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-field label {
  font-size: 14px;
  color: #333;
}

.content-field input,
.content-field textarea {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 一键开课 */
.one-key-open {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.one-key-open span {
  font-size: 14px;
  color: #333;
}

/* 分配配置 */
.distribution-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.config-item span {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.config-item input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 分配条件 */
.distribution-conditions,
.time-conditions {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-row,
.time-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.condition-row span,
.time-row span {
  min-width: 80px;
  font-size: 14px;
  color: #333;
}

.condition-row input,
.condition-row textarea,
.time-row input {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 分配素材 */
.distribution-materials {
  display: flex;
  align-items: center;
  gap: 12px;
}

.distribution-materials span {
  min-width: 80px;
  font-size: 14px;
  color: #333;
}

.materials-grid {
  display: flex;
  gap: 8px;
}

.material-slot {
  width: 60px;
  height: 60px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #333;
  background-color: #f9f9f9;
}

.material-slot.empty {
  background-color: #fff;
  border-style: dashed;
}

/* 分配上传 */
.distribution-upload {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.distribution-upload span {
  min-width: 80px;
  font-size: 14px;
  color: #333;
  margin-top: 8px;
}

.upload-grid {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.upload-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: none;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.upload-slot:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.upload-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 10px;
  text-align: center;
  padding: 4px;
  line-height: 1.2;
}
