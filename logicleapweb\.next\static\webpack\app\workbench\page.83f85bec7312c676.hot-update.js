"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseManagement.tsx":
/*!*******************************************************!*\
  !*** ./app/workbench/components/CourseManagement.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _CreateSeriesCourseModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CreateSeriesCourseModal */ \"(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx\");\n/* harmony import */ var _CourseListEditModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CourseListEditModal */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _CourseManagement_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CourseManagement.css */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CourseManagement = ()=>{\n    _s();\n    const [isCreateModalVisible, setIsCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSeriesModalVisible, setIsSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        tags: \"\"\n    });\n    // 课程数据状态\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 课程列表编辑弹窗状态\n    const [isCourseListModalVisible, setIsCourseListModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取课程列表\n    const fetchCourses = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_4__.courseManagementApi.getMySeries({\n                page: 1,\n                pageSize: 20\n            });\n            if (response.code === 200) {\n                // 转换API数据格式为本地Course格式\n                const coursesData = response.data.list.map((item)=>({\n                        id: item.id,\n                        title: item.title,\n                        description: item.description,\n                        coverImage: item.coverImage,\n                        status: item.status === 0 ? \"draft\" : item.status === 1 ? \"published\" : \"offline\",\n                        statusLabel: item.statusLabel,\n                        totalCourses: item.totalCourses,\n                        totalStudents: item.totalStudents,\n                        tags: item.tags || [],\n                        createTime: new Date(item.createdAt).toLocaleDateString(\"zh-CN\", {\n                            year: \"numeric\",\n                            month: \"2-digit\",\n                            day: \"2-digit\"\n                        }).replace(/\\//g, \".\")\n                    }));\n                setCourses(coursesData);\n                console.log(\"课程列表加载成功:\", coursesData);\n            } else {\n                throw new Error(response.message || \"获取课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"获取课程列表失败:\", error);\n            setError(error instanceof Error ? error.message : \"获取课程列表失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 组件挂载时获取课程列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourses();\n    }, []);\n    // 处理创建课程\n    const handleCreateCourse = async (e)=>{\n        if (e) e.preventDefault();\n        // 检查标题是否为空\n        if (!formData.title.trim()) {\n            alert(\"请输入系列课程标题\");\n            return;\n        }\n        try {\n            console.log(\"创建系列课程数据:\", formData);\n            // 添加新课程到列表\n            const newCourse = {\n                id: courses.length + 1,\n                title: formData.title,\n                description: \"创建于\".concat(new Date().getFullYear(), \".\").concat(String(new Date().getMonth() + 1).padStart(2, \"0\"), \".\").concat(String(new Date().getDate()).padStart(2, \"0\")),\n                coverImage: \"/api/placeholder/280/160\",\n                status: \"draft\",\n                createTime: \"\".concat(new Date().getFullYear(), \".\").concat(String(new Date().getMonth() + 1).padStart(2, \"0\"), \".\").concat(String(new Date().getDate()).padStart(2, \"0\"))\n            };\n            setCourses((prev)=>[\n                    newCourse,\n                    ...prev\n                ]);\n            alert(\"系列课程创建成功！\");\n            setIsCreateModalVisible(false);\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"\",\n                tags: \"\"\n            });\n        } catch (error) {\n            console.error(\"创建系列课程失败:\", error);\n            alert(\"创建系列课程失败，请重试\");\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // 处理系列课程创建\n    const handleCreateSeriesCourse = async (data)=>{\n        try {\n            console.log(\"创建系列课程成功:\", data);\n            // 刷新课程列表以获取最新数据\n            await fetchCourses();\n            console.log(\"课程列表已刷新\");\n        } catch (error) {\n            console.error(\"刷新课程列表失败:\", error);\n        }\n    };\n    // 处理课程卡片点击\n    const handleCourseCardClick = (course)=>{\n        console.log(\"\\uD83C\\uDFAF 点击课程卡片:\", course);\n        console.log(\"\\uD83D\\uDD0D 传递的seriesId:\", course.id);\n        setSelectedCourse(course);\n        setIsCourseListModalVisible(true);\n    };\n    // 处理课程列表弹窗关闭\n    const handleCourseListModalClose = ()=>{\n        setIsCourseListModalVisible(false);\n        setSelectedCourse(null);\n    };\n    // 处理课程列表保存\n    const handleCourseListSave = (data)=>{\n        console.log(\"保存课程列表数据:\", data);\n    // 这里可以添加保存逻辑\n    };\n    // 获取状态按钮样式和文本\n    const getStatusButton = (status)=>{\n        switch(status){\n            case \"draft\":\n                return {\n                    text: \"草稿\",\n                    className: \"px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm border border-gray-300\"\n                };\n            case \"published\":\n                return {\n                    text: \"发布\",\n                    className: \"px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm border border-blue-300\"\n                };\n            case \"offline\":\n                return {\n                    text: \"下架\",\n                    className: \"px-4 py-1 bg-orange-100 text-orange-600 rounded-full text-sm border border-orange-300\"\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"课程管理\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsSeriesModalVisible(true),\n                            className: \"create-course-btn\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"create-course-btn-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"创建课程\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"正在获取课程列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"加载失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchCourses,\n                                className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium mx-auto\",\n                                children: \"重新加载\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, undefined) : courses.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"暂无课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: \"您还没有创建任何课程，点击右上角按钮开始创建您的第一个课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsSeriesModalVisible(true),\n                                className: \"flex items-center gap-2 px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200 font-medium mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"创建第一个课程\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: courses.map((course)=>{\n                        const statusButton = getStatusButton(course.status);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-card cursor-pointer hover:shadow-lg transition-shadow duration-200\",\n                            onClick: ()=>handleCourseCardClick(course),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-cover\",\n                                    children: course.coverImage ? // 有封面图片时显示背景图（包括占位符）\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full\",\n                                        style: {\n                                            backgroundImage: \"url(\".concat(course.coverImage, \")\"),\n                                            backgroundSize: \"cover\",\n                                            backgroundPosition: \"center\",\n                                            backgroundRepeat: \"no-repeat\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 23\n                                    }, undefined) : // 没有封面时显示灰色背景和图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 23\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"course-title\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: statusButton.className,\n                                                    children: statusButton.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"课程 \",\n                                                            course.totalCourses || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.createTime\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, course.id, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 17\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined),\n            isCreateModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"创建系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setIsCreateModalVisible(false);\n                                        setFormData({\n                                            title: \"\",\n                                            description: \"\",\n                                            category: \"\",\n                                            tags: \"\"\n                                        });\n                                    },\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-32 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"系列课程封面\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"title\",\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        placeholder: \"系列课程标题\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors text-center\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateCourse,\n                                        className: \"px-8 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                                        children: \"创建系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateSeriesCourseModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isVisible: isSeriesModalVisible,\n                onClose: ()=>setIsSeriesModalVisible(false),\n                onSubmit: handleCreateSeriesCourse\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, undefined),\n            selectedCourse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseListEditModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: isCourseListModalVisible,\n                onClose: handleCourseListModalClose,\n                onSave: handleCourseListSave,\n                seriesTitle: selectedCourse.title,\n                seriesCoverImage: selectedCourse.coverImage,\n                seriesId: selectedCourse.id\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 373,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseManagement, \"OAh8Sxvi/BHunp9NjEQlOqcwLhQ=\");\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\n"));

/***/ })

});