"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-mentions";
exports.ids = ["vendor-chunks/rc-mentions"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-mentions/es/DropdownMenu.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-mentions/es/DropdownMenu.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_menu__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-menu */ \"(ssr)/./node_modules/rc-menu/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MentionsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MentionsContext */ \"(ssr)/./node_modules/rc-mentions/es/MentionsContext.js\");\n\n\n\n/**\n * We only use Menu to display the candidate.\n * The focus is controlled by textarea to make accessibility easy.\n */\nfunction DropdownMenu(props) {\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(_MentionsContext__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n    notFoundContent = _React$useContext.notFoundContent,\n    activeIndex = _React$useContext.activeIndex,\n    setActiveIndex = _React$useContext.setActiveIndex,\n    selectOption = _React$useContext.selectOption,\n    onFocus = _React$useContext.onFocus,\n    onBlur = _React$useContext.onBlur;\n  var prefixCls = props.prefixCls,\n    options = props.options;\n  var activeOption = options[activeIndex] || {};\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_0__[\"default\"], {\n    prefixCls: \"\".concat(prefixCls, \"-menu\"),\n    activeKey: activeOption.key,\n    onSelect: function onSelect(_ref) {\n      var key = _ref.key;\n      var option = options.find(function (_ref2) {\n        var optionKey = _ref2.key;\n        return optionKey === key;\n      });\n      selectOption(option);\n    },\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, options.map(function (option, index) {\n    var key = option.key,\n      disabled = option.disabled,\n      className = option.className,\n      style = option.style,\n      label = option.label;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_0__.MenuItem, {\n      key: key,\n      disabled: disabled,\n      className: className,\n      style: style,\n      onMouseEnter: function onMouseEnter() {\n        setActiveIndex(index);\n      }\n    }, label);\n  }), !options.length && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_0__.MenuItem, {\n    disabled: true\n  }, notFoundContent));\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DropdownMenu);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/DropdownMenu.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/KeywordTrigger.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-mentions/es/KeywordTrigger.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rc_component_trigger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @rc-component/trigger */ \"(ssr)/./node_modules/@rc-component/trigger/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _DropdownMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DropdownMenu */ \"(ssr)/./node_modules/rc-mentions/es/DropdownMenu.js\");\n\n\n\n\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\nvar KeywordTrigger = function KeywordTrigger(props) {\n  var prefixCls = props.prefixCls,\n    options = props.options,\n    children = props.children,\n    visible = props.visible,\n    transitionName = props.transitionName,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    direction = props.direction,\n    placement = props.placement;\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var dropdownElement = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_DropdownMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    prefixCls: dropdownPrefix,\n    options: options\n  });\n  var dropdownPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    var popupPlacement;\n    if (direction === 'rtl') {\n      popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n    } else {\n      popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n    }\n    return popupPlacement;\n  }, [direction, placement]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_rc_component_trigger__WEBPACK_IMPORTED_MODULE_0__[\"default\"], {\n    prefixCls: dropdownPrefix,\n    popupVisible: visible,\n    popup: dropdownElement,\n    popupPlacement: dropdownPlacement,\n    popupTransitionName: transitionName,\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    getPopupContainer: getPopupContainer,\n    popupClassName: dropdownClassName\n  }, children);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (KeywordTrigger);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvS2V5d29yZFRyaWdnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEM7QUFDYjtBQUNDO0FBQ1U7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGdEQUFtQixDQUFDLHFEQUFZO0FBQ3JFO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsMEJBQTBCLDhDQUFPO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLGdEQUFtQixDQUFDLDZEQUFPO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxpRUFBZSxjQUFjIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW1lbnRpb25zL2VzL0tleXdvcmRUcmlnZ2VyLmpzPzQyZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFRyaWdnZXIgZnJvbSAnQHJjLWNvbXBvbmVudC90cmlnZ2VyJztcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRHJvcGRvd25NZW51IGZyb20gXCIuL0Ryb3Bkb3duTWVudVwiO1xudmFyIEJVSUxUX0lOX1BMQUNFTUVOVFMgPSB7XG4gIGJvdHRvbVJpZ2h0OiB7XG4gICAgcG9pbnRzOiBbJ3RsJywgJ2JyJ10sXG4gICAgb2Zmc2V0OiBbMCwgNF0sXG4gICAgb3ZlcmZsb3c6IHtcbiAgICAgIGFkanVzdFg6IDEsXG4gICAgICBhZGp1c3RZOiAxXG4gICAgfVxuICB9LFxuICBib3R0b21MZWZ0OiB7XG4gICAgcG9pbnRzOiBbJ3RyJywgJ2JsJ10sXG4gICAgb2Zmc2V0OiBbMCwgNF0sXG4gICAgb3ZlcmZsb3c6IHtcbiAgICAgIGFkanVzdFg6IDEsXG4gICAgICBhZGp1c3RZOiAxXG4gICAgfVxuICB9LFxuICB0b3BSaWdodDoge1xuICAgIHBvaW50czogWydibCcsICd0ciddLFxuICAgIG9mZnNldDogWzAsIC00XSxcbiAgICBvdmVyZmxvdzoge1xuICAgICAgYWRqdXN0WDogMSxcbiAgICAgIGFkanVzdFk6IDFcbiAgICB9XG4gIH0sXG4gIHRvcExlZnQ6IHtcbiAgICBwb2ludHM6IFsnYnInLCAndGwnXSxcbiAgICBvZmZzZXQ6IFswLCAtNF0sXG4gICAgb3ZlcmZsb3c6IHtcbiAgICAgIGFkanVzdFg6IDEsXG4gICAgICBhZGp1c3RZOiAxXG4gICAgfVxuICB9XG59O1xudmFyIEtleXdvcmRUcmlnZ2VyID0gZnVuY3Rpb24gS2V5d29yZFRyaWdnZXIocHJvcHMpIHtcbiAgdmFyIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscyxcbiAgICBvcHRpb25zID0gcHJvcHMub3B0aW9ucyxcbiAgICBjaGlsZHJlbiA9IHByb3BzLmNoaWxkcmVuLFxuICAgIHZpc2libGUgPSBwcm9wcy52aXNpYmxlLFxuICAgIHRyYW5zaXRpb25OYW1lID0gcHJvcHMudHJhbnNpdGlvbk5hbWUsXG4gICAgZ2V0UG9wdXBDb250YWluZXIgPSBwcm9wcy5nZXRQb3B1cENvbnRhaW5lcixcbiAgICBkcm9wZG93bkNsYXNzTmFtZSA9IHByb3BzLmRyb3Bkb3duQ2xhc3NOYW1lLFxuICAgIGRpcmVjdGlvbiA9IHByb3BzLmRpcmVjdGlvbixcbiAgICBwbGFjZW1lbnQgPSBwcm9wcy5wbGFjZW1lbnQ7XG4gIHZhciBkcm9wZG93blByZWZpeCA9IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItZHJvcGRvd25cIik7XG4gIHZhciBkcm9wZG93bkVsZW1lbnQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChEcm9wZG93bk1lbnUsIHtcbiAgICBwcmVmaXhDbHM6IGRyb3Bkb3duUHJlZml4LFxuICAgIG9wdGlvbnM6IG9wdGlvbnNcbiAgfSk7XG4gIHZhciBkcm9wZG93blBsYWNlbWVudCA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciBwb3B1cFBsYWNlbWVudDtcbiAgICBpZiAoZGlyZWN0aW9uID09PSAncnRsJykge1xuICAgICAgcG9wdXBQbGFjZW1lbnQgPSBwbGFjZW1lbnQgPT09ICd0b3AnID8gJ3RvcExlZnQnIDogJ2JvdHRvbUxlZnQnO1xuICAgIH0gZWxzZSB7XG4gICAgICBwb3B1cFBsYWNlbWVudCA9IHBsYWNlbWVudCA9PT0gJ3RvcCcgPyAndG9wUmlnaHQnIDogJ2JvdHRvbVJpZ2h0JztcbiAgICB9XG4gICAgcmV0dXJuIHBvcHVwUGxhY2VtZW50O1xuICB9LCBbZGlyZWN0aW9uLCBwbGFjZW1lbnRdKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFRyaWdnZXIsIHtcbiAgICBwcmVmaXhDbHM6IGRyb3Bkb3duUHJlZml4LFxuICAgIHBvcHVwVmlzaWJsZTogdmlzaWJsZSxcbiAgICBwb3B1cDogZHJvcGRvd25FbGVtZW50LFxuICAgIHBvcHVwUGxhY2VtZW50OiBkcm9wZG93blBsYWNlbWVudCxcbiAgICBwb3B1cFRyYW5zaXRpb25OYW1lOiB0cmFuc2l0aW9uTmFtZSxcbiAgICBidWlsdGluUGxhY2VtZW50czogQlVJTFRfSU5fUExBQ0VNRU5UUyxcbiAgICBnZXRQb3B1cENvbnRhaW5lcjogZ2V0UG9wdXBDb250YWluZXIsXG4gICAgcG9wdXBDbGFzc05hbWU6IGRyb3Bkb3duQ2xhc3NOYW1lXG4gIH0sIGNoaWxkcmVuKTtcbn07XG5leHBvcnQgZGVmYXVsdCBLZXl3b3JkVHJpZ2dlcjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/KeywordTrigger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/Mentions.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-mentions/es/Mentions.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-textarea */ \"(ssr)/./node_modules/rc-textarea/es/index.js\");\n/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/Children/toArray */ \"(ssr)/./node_modules/rc-util/es/Children/toArray.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-mentions/es/hooks/useEffectState.js\");\n/* harmony import */ var _KeywordTrigger__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./KeywordTrigger */ \"(ssr)/./node_modules/rc-mentions/es/KeywordTrigger.js\");\n/* harmony import */ var _MentionsContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./MentionsContext */ \"(ssr)/./node_modules/rc-mentions/es/MentionsContext.js\");\n/* harmony import */ var _Option__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./Option */ \"(ssr)/./node_modules/rc-mentions/es/Option.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/rc-mentions/es/util.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"className\", \"style\", \"prefix\", \"split\", \"notFoundContent\", \"value\", \"defaultValue\", \"children\", \"options\", \"open\", \"allowClear\", \"silent\", \"validateSearch\", \"filterOption\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onPressEnter\", \"onSearch\", \"onSelect\", \"onFocus\", \"onBlur\", \"transitionName\", \"placement\", \"direction\", \"getPopupContainer\", \"dropdownClassName\", \"rows\", \"visible\"],\n  _excluded2 = [\"suffix\", \"prefixCls\", \"defaultValue\", \"value\", \"allowClear\", \"onChange\", \"classNames\", \"className\", \"disabled\", \"onClear\"];\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar InternalMentions = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_11__.forwardRef)(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    _props$prefix = props.prefix,\n    prefix = _props$prefix === void 0 ? '@' : _props$prefix,\n    _props$split = props.split,\n    split = _props$split === void 0 ? ' ' : _props$split,\n    _props$notFoundConten = props.notFoundContent,\n    notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    children = props.children,\n    options = props.options,\n    open = props.open,\n    allowClear = props.allowClear,\n    silent = props.silent,\n    _props$validateSearch = props.validateSearch,\n    validateSearch = _props$validateSearch === void 0 ? _util__WEBPACK_IMPORTED_MODULE_16__.validateSearch : _props$validateSearch,\n    _props$filterOption = props.filterOption,\n    filterOption = _props$filterOption === void 0 ? _util__WEBPACK_IMPORTED_MODULE_16__.filterOption : _props$filterOption,\n    onChange = props.onChange,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp,\n    onPressEnter = props.onPressEnter,\n    onSearch = props.onSearch,\n    onSelect = props.onSelect,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    transitionName = props.transitionName,\n    placement = props.placement,\n    direction = props.direction,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    _props$rows = props.rows,\n    rows = _props$rows === void 0 ? 1 : _props$rows,\n    visible = props.visible,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var mergedPrefix = (0,react__WEBPACK_IMPORTED_MODULE_11__.useMemo)(function () {\n    return Array.isArray(prefix) ? prefix : [prefix];\n  }, [prefix]);\n\n  // =============================== Refs ===============================\n  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var measureRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var getTextArea = function getTextArea() {\n    var _textareaRef$current;\n    return (_textareaRef$current = textareaRef.current) === null || _textareaRef$current === void 0 || (_textareaRef$current = _textareaRef$current.resizableTextArea) === null || _textareaRef$current === void 0 ? void 0 : _textareaRef$current.textArea;\n  };\n  react__WEBPACK_IMPORTED_MODULE_11___default().useImperativeHandle(ref, function () {\n    var _textareaRef$current4;\n    return {\n      focus: function focus() {\n        var _textareaRef$current2;\n        return (_textareaRef$current2 = textareaRef.current) === null || _textareaRef$current2 === void 0 ? void 0 : _textareaRef$current2.focus();\n      },\n      blur: function blur() {\n        var _textareaRef$current3;\n        return (_textareaRef$current3 = textareaRef.current) === null || _textareaRef$current3 === void 0 ? void 0 : _textareaRef$current3.blur();\n      },\n      textarea: (_textareaRef$current4 = textareaRef.current) === null || _textareaRef$current4 === void 0 || (_textareaRef$current4 = _textareaRef$current4.resizableTextArea) === null || _textareaRef$current4 === void 0 ? void 0 : _textareaRef$current4.textArea,\n      nativeElement: containerRef.current\n    };\n  });\n\n  // ============================== State ===============================\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(false),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    measuring = _useState2[0],\n    setMeasuring = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(''),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    measureText = _useState4[0],\n    setMeasureText = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(''),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    measurePrefix = _useState6[0],\n    setMeasurePrefix = _useState6[1];\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(0),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState7, 2),\n    measureLocation = _useState8[0],\n    setMeasureLocation = _useState8[1];\n  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(0),\n    _useState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState9, 2),\n    activeIndex = _useState10[0],\n    setActiveIndex = _useState10[1];\n  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(false),\n    _useState12 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState11, 2),\n    isFocus = _useState12[0],\n    setIsFocus = _useState12[1];\n\n  // ============================== Value ===============================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_8__[\"default\"])('', {\n      defaultValue: defaultValue,\n      value: value\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n\n  // =============================== Open ===============================\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {\n    // Sync measure div top with textarea for rc-trigger usage\n    if (measuring && measureRef.current) {\n      measureRef.current.scrollTop = getTextArea().scrollTop;\n    }\n  }, [measuring]);\n  var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11___default().useMemo(function () {\n      if (open) {\n        if (true) {\n          (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(false, '`open` of Mentions is only used for debug usage. Do not use in you production.');\n        }\n        for (var i = 0; i < mergedPrefix.length; i += 1) {\n          var curPrefix = mergedPrefix[i];\n          var index = mergedValue.lastIndexOf(curPrefix);\n          if (index >= 0) {\n            return [true, '', curPrefix, index];\n          }\n        }\n      }\n      return [measuring, measureText, measurePrefix, measureLocation];\n    }, [open, measuring, mergedPrefix, mergedValue, measureText, measurePrefix, measureLocation]),\n    _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useMemo, 4),\n    mergedMeasuring = _React$useMemo2[0],\n    mergedMeasureText = _React$useMemo2[1],\n    mergedMeasurePrefix = _React$useMemo2[2],\n    mergedMeasureLocation = _React$useMemo2[3];\n\n  // ============================== Option ==============================\n  var getOptions = react__WEBPACK_IMPORTED_MODULE_11___default().useCallback(function (targetMeasureText) {\n    var list;\n    if (options && options.length > 0) {\n      list = options.map(function (item) {\n        var _item$key;\n        return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, item), {}, {\n          key: (_item$key = item === null || item === void 0 ? void 0 : item.key) !== null && _item$key !== void 0 ? _item$key : item.value\n        });\n      });\n    } else {\n      list = (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(children).map(function (_ref) {\n        var optionProps = _ref.props,\n          key = _ref.key;\n        return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, optionProps), {}, {\n          label: optionProps.children,\n          key: key || optionProps.value\n        });\n      });\n    }\n    return list.filter(function (option) {\n      /** Return all result if `filterOption` is false. */\n      if (filterOption === false) {\n        return true;\n      }\n      return filterOption(targetMeasureText, option);\n    });\n  }, [children, options, filterOption]);\n  var mergedOptions = react__WEBPACK_IMPORTED_MODULE_11___default().useMemo(function () {\n    return getOptions(mergedMeasureText);\n  }, [getOptions, mergedMeasureText]);\n\n  // ============================= Measure ==============================\n  // Mark that we will reset input selection to target position when user select option\n  var onSelectionEffect = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n  var startMeasure = function startMeasure(nextMeasureText, nextMeasurePrefix, nextMeasureLocation) {\n    setMeasuring(true);\n    setMeasureText(nextMeasureText);\n    setMeasurePrefix(nextMeasurePrefix);\n    setMeasureLocation(nextMeasureLocation);\n    setActiveIndex(0);\n  };\n  var stopMeasure = function stopMeasure(callback) {\n    setMeasuring(false);\n    setMeasureLocation(0);\n    setMeasureText('');\n    onSelectionEffect(callback);\n  };\n\n  // ============================== Change ==============================\n  var triggerChange = function triggerChange(nextValue) {\n    setMergedValue(nextValue);\n    onChange === null || onChange === void 0 || onChange(nextValue);\n  };\n  var onInternalChange = function onInternalChange(_ref2) {\n    var nextValue = _ref2.target.value;\n    triggerChange(nextValue);\n  };\n  var selectOption = function selectOption(option) {\n    var _getTextArea;\n    var _option$value = option.value,\n      mentionValue = _option$value === void 0 ? '' : _option$value;\n    var _replaceWithMeasure = (0,_util__WEBPACK_IMPORTED_MODULE_16__.replaceWithMeasure)(mergedValue, {\n        measureLocation: mergedMeasureLocation,\n        targetText: mentionValue,\n        prefix: mergedMeasurePrefix,\n        selectionStart: (_getTextArea = getTextArea()) === null || _getTextArea === void 0 ? void 0 : _getTextArea.selectionStart,\n        split: split\n      }),\n      text = _replaceWithMeasure.text,\n      selectionLocation = _replaceWithMeasure.selectionLocation;\n    triggerChange(text);\n    stopMeasure(function () {\n      // We need restore the selection position\n      (0,_util__WEBPACK_IMPORTED_MODULE_16__.setInputSelection)(getTextArea(), selectionLocation);\n    });\n    onSelect === null || onSelect === void 0 || onSelect(option, mergedMeasurePrefix);\n  };\n\n  // ============================= KeyEvent =============================\n  // Check if hit the measure keyword\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var which = event.which;\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n\n    // Skip if not measuring\n    if (!mergedMeasuring) {\n      return;\n    }\n    if (which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].UP || which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].DOWN) {\n      // Control arrow function\n      var optionLen = mergedOptions.length;\n      var offset = which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].UP ? -1 : 1;\n      var newActiveIndex = (activeIndex + offset + optionLen) % optionLen;\n      setActiveIndex(newActiveIndex);\n      event.preventDefault();\n    } else if (which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].ESC) {\n      stopMeasure();\n    } else if (which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].ENTER) {\n      // Measure hit\n      event.preventDefault();\n      // loading skip\n      if (silent) {\n        return;\n      }\n      if (!mergedOptions.length) {\n        stopMeasure();\n        return;\n      }\n      var _option = mergedOptions[activeIndex];\n      selectOption(_option);\n    }\n  };\n\n  /**\n   * When to start measure:\n   * 1. When user press `prefix`\n   * 2. When measureText !== prevMeasureText\n   *  - If measure hit\n   *  - If measuring\n   *\n   * When to stop measure:\n   * 1. Selection is out of range\n   * 2. Contains `space`\n   * 3. ESC or select one\n   */\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    var key = event.key,\n      which = event.which;\n    var target = event.target;\n    var selectionStartText = (0,_util__WEBPACK_IMPORTED_MODULE_16__.getBeforeSelectionText)(target);\n    var _getLastMeasureIndex = (0,_util__WEBPACK_IMPORTED_MODULE_16__.getLastMeasureIndex)(selectionStartText, mergedPrefix),\n      measureIndex = _getLastMeasureIndex.location,\n      nextMeasurePrefix = _getLastMeasureIndex.prefix;\n\n    // If the client implements an onKeyUp handler, call it\n    onKeyUp === null || onKeyUp === void 0 || onKeyUp(event);\n\n    // Skip if match the white key list\n    if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].ESC, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].UP, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].DOWN, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].ENTER].indexOf(which) !== -1) {\n      return;\n    }\n    if (measureIndex !== -1) {\n      var nextMeasureText = selectionStartText.slice(measureIndex + nextMeasurePrefix.length);\n      var validateMeasure = validateSearch(nextMeasureText, split);\n      var matchOption = !!getOptions(nextMeasureText).length;\n      if (validateMeasure) {\n        // adding AltGraph also fort azert keyboard\n        if (key === nextMeasurePrefix || key === 'Shift' || which === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_9__[\"default\"].ALT || key === 'AltGraph' || mergedMeasuring || nextMeasureText !== mergedMeasureText && matchOption) {\n          startMeasure(nextMeasureText, nextMeasurePrefix, measureIndex);\n        }\n      } else if (mergedMeasuring) {\n        // Stop if measureText is invalidate\n        stopMeasure();\n      }\n\n      /**\n       * We will trigger `onSearch` to developer since they may use for async update.\n       * If met `space` means user finished searching.\n       */\n      if (onSearch && validateMeasure) {\n        onSearch(nextMeasureText, nextMeasurePrefix);\n      }\n    } else if (mergedMeasuring) {\n      stopMeasure();\n    }\n  };\n  var onInternalPressEnter = function onInternalPressEnter(event) {\n    if (!mergedMeasuring && onPressEnter) {\n      onPressEnter(event);\n    }\n  };\n\n  // ============================ Focus Blur ============================\n  var focusRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)();\n  var onInternalFocus = function onInternalFocus(event) {\n    window.clearTimeout(focusRef.current);\n    if (!isFocus && event && onFocus) {\n      onFocus(event);\n    }\n    setIsFocus(true);\n  };\n  var onInternalBlur = function onInternalBlur(event) {\n    focusRef.current = window.setTimeout(function () {\n      setIsFocus(false);\n      stopMeasure();\n      onBlur === null || onBlur === void 0 || onBlur(event);\n    }, 0);\n  };\n  var onDropdownFocus = function onDropdownFocus() {\n    onInternalFocus();\n  };\n  var onDropdownBlur = function onDropdownBlur() {\n    onInternalBlur();\n  };\n\n  // ============================== Render ==============================\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(prefixCls, className),\n    style: style,\n    ref: containerRef\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_textarea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: textareaRef,\n    value: mergedValue\n  }, restProps, {\n    rows: rows,\n    onChange: onInternalChange,\n    onKeyDown: onInternalKeyDown,\n    onKeyUp: onInternalKeyUp,\n    onPressEnter: onInternalPressEnter,\n    onFocus: onInternalFocus,\n    onBlur: onInternalBlur\n  })), mergedMeasuring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"div\", {\n    ref: measureRef,\n    className: \"\".concat(prefixCls, \"-measure\")\n  }, mergedValue.slice(0, mergedMeasureLocation), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_MentionsContext__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Provider, {\n    value: {\n      notFoundContent: notFoundContent,\n      activeIndex: activeIndex,\n      setActiveIndex: setActiveIndex,\n      selectOption: selectOption,\n      onFocus: onDropdownFocus,\n      onBlur: onDropdownBlur\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_KeywordTrigger__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n    prefixCls: prefixCls,\n    transitionName: transitionName,\n    placement: placement,\n    direction: direction,\n    options: mergedOptions,\n    visible: true,\n    getPopupContainer: getPopupContainer,\n    dropdownClassName: dropdownClassName\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", null, mergedMeasurePrefix))), mergedValue.slice(mergedMeasureLocation + mergedMeasurePrefix.length)));\n});\nvar Mentions = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_11__.forwardRef)(function (_ref3, ref) {\n  var suffix = _ref3.suffix,\n    _ref3$prefixCls = _ref3.prefixCls,\n    prefixCls = _ref3$prefixCls === void 0 ? 'rc-mentions' : _ref3$prefixCls,\n    defaultValue = _ref3.defaultValue,\n    customValue = _ref3.value,\n    allowClear = _ref3.allowClear,\n    onChange = _ref3.onChange,\n    classes = _ref3.classNames,\n    className = _ref3.className,\n    disabled = _ref3.disabled,\n    onClear = _ref3.onClear,\n    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref3, _excluded2);\n  // =============================== Ref ================================\n  var holderRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var mentionRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function () {\n    var _holderRef$current, _mentionRef$current;\n    return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mentionRef.current), {}, {\n      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || ((_mentionRef$current = mentionRef.current) === null || _mentionRef$current === void 0 ? void 0 : _mentionRef$current.nativeElement)\n    });\n  });\n\n  // ============================== Value ===============================\n  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_8__[\"default\"])('', {\n      defaultValue: defaultValue,\n      value: customValue\n    }),\n    _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useMergedState3, 2),\n    mergedValue = _useMergedState4[0],\n    setMergedValue = _useMergedState4[1];\n\n  // ============================== Change ==============================\n  var triggerChange = function triggerChange(currentValue) {\n    setMergedValue(currentValue);\n    onChange === null || onChange === void 0 || onChange(currentValue);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset() {\n    triggerChange('');\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_5__.BaseInput, {\n    suffix: suffix,\n    prefixCls: prefixCls,\n    value: mergedValue,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    className: className,\n    classNames: classes,\n    disabled: disabled,\n    ref: holderRef,\n    onClear: onClear\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(InternalMentions, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classes === null || classes === void 0 ? void 0 : classes.mentions,\n    prefixCls: prefixCls,\n    ref: mentionRef,\n    onChange: triggerChange,\n    disabled: disabled\n  }, rest)));\n});\nMentions.Option = _Option__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Mentions);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/Mentions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/MentionsContext.js":
/*!********************************************************!*\
  !*** ./node_modules/rc-mentions/es/MentionsContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* tslint:disable: no-object-literal-type-assertion */\n\n// We will never use default, here only to fix TypeScript warning\nvar MentionsContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MentionsContext);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvTWVudGlvbnNDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsbUNBQW1DLGdEQUFtQjtBQUN0RCxpRUFBZSxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW1lbnRpb25zL2VzL01lbnRpb25zQ29udGV4dC5qcz8wNGU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIHRzbGludDpkaXNhYmxlOiBuby1vYmplY3QtbGl0ZXJhbC10eXBlLWFzc2VydGlvbiAqL1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuLy8gV2Ugd2lsbCBuZXZlciB1c2UgZGVmYXVsdCwgaGVyZSBvbmx5IHRvIGZpeCBUeXBlU2NyaXB0IHdhcm5pbmdcbnZhciBNZW50aW9uc0NvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dChudWxsKTtcbmV4cG9ydCBkZWZhdWx0IE1lbnRpb25zQ29udGV4dDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/MentionsContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/Option.js":
/*!***********************************************!*\
  !*** ./node_modules/rc-mentions/es/Option.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar Option = function Option() {\n  return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Option);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvT3B0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxNQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW1lbnRpb25zL2VzL09wdGlvbi5qcz8zZDRjIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBPcHRpb24gPSBmdW5jdGlvbiBPcHRpb24oKSB7XG4gIHJldHVybiBudWxsO1xufTtcbmV4cG9ydCBkZWZhdWx0IE9wdGlvbjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/Option.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-mentions/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Trigger a callback on state change\n */\nfunction useEffectState() {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n      id: 0,\n      callback: null\n    }),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    effectId = _useState2[0],\n    setEffectId = _useState2[1];\n  var update = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (callback) {\n    setEffectId(function (_ref) {\n      var id = _ref.id;\n      return {\n        id: id + 1,\n        callback: callback\n      };\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var _effectId$callback;\n    (_effectId$callback = effectId.callback) === null || _effectId$callback === void 0 || _effectId$callback.call(effectId);\n  }, [effectId]);\n  return update;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvaG9va3MvdXNlRWZmZWN0U3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUNiO0FBQ3pEO0FBQ0E7QUFDQTtBQUNlO0FBQ2Ysa0JBQWtCLCtDQUFRO0FBQzFCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxlQUFlLGtEQUFXO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1tZW50aW9ucy9lcy9ob29rcy91c2VFZmZlY3RTdGF0ZS5qcz8yYWJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG4vKipcbiAqIFRyaWdnZXIgYSBjYWxsYmFjayBvbiBzdGF0ZSBjaGFuZ2VcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlRWZmZWN0U3RhdGUoKSB7XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZSh7XG4gICAgICBpZDogMCxcbiAgICAgIGNhbGxiYWNrOiBudWxsXG4gICAgfSksXG4gICAgX3VzZVN0YXRlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZSwgMiksXG4gICAgZWZmZWN0SWQgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldEVmZmVjdElkID0gX3VzZVN0YXRlMlsxXTtcbiAgdmFyIHVwZGF0ZSA9IHVzZUNhbGxiYWNrKGZ1bmN0aW9uIChjYWxsYmFjaykge1xuICAgIHNldEVmZmVjdElkKGZ1bmN0aW9uIChfcmVmKSB7XG4gICAgICB2YXIgaWQgPSBfcmVmLmlkO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgaWQ6IGlkICsgMSxcbiAgICAgICAgY2FsbGJhY2s6IGNhbGxiYWNrXG4gICAgICB9O1xuICAgIH0pO1xuICB9LCBbXSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgdmFyIF9lZmZlY3RJZCRjYWxsYmFjaztcbiAgICAoX2VmZmVjdElkJGNhbGxiYWNrID0gZWZmZWN0SWQuY2FsbGJhY2spID09PSBudWxsIHx8IF9lZmZlY3RJZCRjYWxsYmFjayA9PT0gdm9pZCAwIHx8IF9lZmZlY3RJZCRjYWxsYmFjay5jYWxsKGVmZmVjdElkKTtcbiAgfSwgW2VmZmVjdElkXSk7XG4gIHJldHVybiB1cGRhdGU7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-mentions/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Mentions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Mentions */ \"(ssr)/./node_modules/rc-mentions/es/Mentions.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Mentions__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvaW5kZXguanM/YmNlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTWVudGlvbnMgZnJvbSBcIi4vTWVudGlvbnNcIjtcbmV4cG9ydCBkZWZhdWx0IE1lbnRpb25zOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-mentions/es/util.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-mentions/es/util.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterOption: () => (/* binding */ filterOption),\n/* harmony export */   getBeforeSelectionText: () => (/* binding */ getBeforeSelectionText),\n/* harmony export */   getLastMeasureIndex: () => (/* binding */ getLastMeasureIndex),\n/* harmony export */   replaceWithMeasure: () => (/* binding */ replaceWithMeasure),\n/* harmony export */   setInputSelection: () => (/* binding */ setInputSelection),\n/* harmony export */   validateSearch: () => (/* binding */ validateSearch)\n/* harmony export */ });\n/**\n * Cut input selection into 2 part and return text before selection start\n */\nfunction getBeforeSelectionText(input) {\n  var selectionStart = input.selectionStart;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\nfunction getLastMeasureIndex(text, prefix) {\n  return prefix.reduce(function (lastMatch, prefixStr) {\n    var lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  var firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  }\n\n  // Reuse rest text as it can\n  var restText = text;\n  var targetTextLen = targetText.length;\n  for (var i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\nfunction replaceWithMeasure(text, measureConfig) {\n  var measureLocation = measureConfig.measureLocation,\n    prefix = measureConfig.prefix,\n    targetText = measureConfig.targetText,\n    selectionStart = measureConfig.selectionStart,\n    split = measureConfig.split;\n\n  // Before text will append one space if have other text\n  var beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = \"\".concat(beforeMeasureText).concat(split);\n  }\n\n  // Cut duplicate string with current targetText\n  var restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  var connectedStartText = \"\".concat(beforeMeasureText).concat(prefix).concat(targetText).concat(split);\n  return {\n    text: \"\".concat(connectedStartText).concat(restText),\n    selectionLocation: connectedStartText.length\n  };\n}\nfunction setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n  input.blur();\n  input.focus();\n}\nfunction validateSearch(text, split) {\n  return !split || text.indexOf(split) === -1;\n}\nfunction filterOption(input, _ref) {\n  var _ref$value = _ref.value,\n    value = _ref$value === void 0 ? '' : _ref$value;\n  var lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBbUI7QUFDckM7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbWVudGlvbnMvZXMvdXRpbC5qcz8yMjE3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ3V0IGlucHV0IHNlbGVjdGlvbiBpbnRvIDIgcGFydCBhbmQgcmV0dXJuIHRleHQgYmVmb3JlIHNlbGVjdGlvbiBzdGFydFxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0QmVmb3JlU2VsZWN0aW9uVGV4dChpbnB1dCkge1xuICB2YXIgc2VsZWN0aW9uU3RhcnQgPSBpbnB1dC5zZWxlY3Rpb25TdGFydDtcbiAgcmV0dXJuIGlucHV0LnZhbHVlLnNsaWNlKDAsIHNlbGVjdGlvblN0YXJ0KTtcbn1cbi8qKlxuICogRmluZCB0aGUgbGFzdCBtYXRjaCBwcmVmaXggaW5kZXhcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldExhc3RNZWFzdXJlSW5kZXgodGV4dCwgcHJlZml4KSB7XG4gIHJldHVybiBwcmVmaXgucmVkdWNlKGZ1bmN0aW9uIChsYXN0TWF0Y2gsIHByZWZpeFN0cikge1xuICAgIHZhciBsYXN0SW5kZXggPSB0ZXh0Lmxhc3RJbmRleE9mKHByZWZpeFN0cik7XG4gICAgaWYgKGxhc3RJbmRleCA+IGxhc3RNYXRjaC5sb2NhdGlvbikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbG9jYXRpb246IGxhc3RJbmRleCxcbiAgICAgICAgcHJlZml4OiBwcmVmaXhTdHJcbiAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiBsYXN0TWF0Y2g7XG4gIH0sIHtcbiAgICBsb2NhdGlvbjogLTEsXG4gICAgcHJlZml4OiAnJ1xuICB9KTtcbn1cbmZ1bmN0aW9uIGxvd2VyKGNoYXIpIHtcbiAgcmV0dXJuIChjaGFyIHx8ICcnKS50b0xvd2VyQ2FzZSgpO1xufVxuZnVuY3Rpb24gcmVkdWNlVGV4dCh0ZXh0LCB0YXJnZXRUZXh0LCBzcGxpdCkge1xuICB2YXIgZmlyc3RDaGFyID0gdGV4dFswXTtcbiAgaWYgKCFmaXJzdENoYXIgfHwgZmlyc3RDaGFyID09PSBzcGxpdCkge1xuICAgIHJldHVybiB0ZXh0O1xuICB9XG5cbiAgLy8gUmV1c2UgcmVzdCB0ZXh0IGFzIGl0IGNhblxuICB2YXIgcmVzdFRleHQgPSB0ZXh0O1xuICB2YXIgdGFyZ2V0VGV4dExlbiA9IHRhcmdldFRleHQubGVuZ3RoO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IHRhcmdldFRleHRMZW47IGkgKz0gMSkge1xuICAgIGlmIChsb3dlcihyZXN0VGV4dFtpXSkgIT09IGxvd2VyKHRhcmdldFRleHRbaV0pKSB7XG4gICAgICByZXN0VGV4dCA9IHJlc3RUZXh0LnNsaWNlKGkpO1xuICAgICAgYnJlYWs7XG4gICAgfSBlbHNlIGlmIChpID09PSB0YXJnZXRUZXh0TGVuIC0gMSkge1xuICAgICAgcmVzdFRleHQgPSByZXN0VGV4dC5zbGljZSh0YXJnZXRUZXh0TGVuKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJlc3RUZXh0O1xufVxuXG4vKipcbiAqIFBhaW50IHRhcmdldFRleHQgaW50byBjdXJyZW50IHRleHQ6XG4gKiAgdGV4dDogbGl0dGxlQGxpdGVzdFxuICogIHRhcmdldFRleHQ6IGxpZ2h0XG4gKiAgPT4gbGl0dGxlIEBsaWdodCB0ZXN0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByZXBsYWNlV2l0aE1lYXN1cmUodGV4dCwgbWVhc3VyZUNvbmZpZykge1xuICB2YXIgbWVhc3VyZUxvY2F0aW9uID0gbWVhc3VyZUNvbmZpZy5tZWFzdXJlTG9jYXRpb24sXG4gICAgcHJlZml4ID0gbWVhc3VyZUNvbmZpZy5wcmVmaXgsXG4gICAgdGFyZ2V0VGV4dCA9IG1lYXN1cmVDb25maWcudGFyZ2V0VGV4dCxcbiAgICBzZWxlY3Rpb25TdGFydCA9IG1lYXN1cmVDb25maWcuc2VsZWN0aW9uU3RhcnQsXG4gICAgc3BsaXQgPSBtZWFzdXJlQ29uZmlnLnNwbGl0O1xuXG4gIC8vIEJlZm9yZSB0ZXh0IHdpbGwgYXBwZW5kIG9uZSBzcGFjZSBpZiBoYXZlIG90aGVyIHRleHRcbiAgdmFyIGJlZm9yZU1lYXN1cmVUZXh0ID0gdGV4dC5zbGljZSgwLCBtZWFzdXJlTG9jYXRpb24pO1xuICBpZiAoYmVmb3JlTWVhc3VyZVRleHRbYmVmb3JlTWVhc3VyZVRleHQubGVuZ3RoIC0gc3BsaXQubGVuZ3RoXSA9PT0gc3BsaXQpIHtcbiAgICBiZWZvcmVNZWFzdXJlVGV4dCA9IGJlZm9yZU1lYXN1cmVUZXh0LnNsaWNlKDAsIGJlZm9yZU1lYXN1cmVUZXh0Lmxlbmd0aCAtIHNwbGl0Lmxlbmd0aCk7XG4gIH1cbiAgaWYgKGJlZm9yZU1lYXN1cmVUZXh0KSB7XG4gICAgYmVmb3JlTWVhc3VyZVRleHQgPSBcIlwiLmNvbmNhdChiZWZvcmVNZWFzdXJlVGV4dCkuY29uY2F0KHNwbGl0KTtcbiAgfVxuXG4gIC8vIEN1dCBkdXBsaWNhdGUgc3RyaW5nIHdpdGggY3VycmVudCB0YXJnZXRUZXh0XG4gIHZhciByZXN0VGV4dCA9IHJlZHVjZVRleHQodGV4dC5zbGljZShzZWxlY3Rpb25TdGFydCksIHRhcmdldFRleHQuc2xpY2Uoc2VsZWN0aW9uU3RhcnQgLSBtZWFzdXJlTG9jYXRpb24gLSBwcmVmaXgubGVuZ3RoKSwgc3BsaXQpO1xuICBpZiAocmVzdFRleHQuc2xpY2UoMCwgc3BsaXQubGVuZ3RoKSA9PT0gc3BsaXQpIHtcbiAgICByZXN0VGV4dCA9IHJlc3RUZXh0LnNsaWNlKHNwbGl0Lmxlbmd0aCk7XG4gIH1cbiAgdmFyIGNvbm5lY3RlZFN0YXJ0VGV4dCA9IFwiXCIuY29uY2F0KGJlZm9yZU1lYXN1cmVUZXh0KS5jb25jYXQocHJlZml4KS5jb25jYXQodGFyZ2V0VGV4dCkuY29uY2F0KHNwbGl0KTtcbiAgcmV0dXJuIHtcbiAgICB0ZXh0OiBcIlwiLmNvbmNhdChjb25uZWN0ZWRTdGFydFRleHQpLmNvbmNhdChyZXN0VGV4dCksXG4gICAgc2VsZWN0aW9uTG9jYXRpb246IGNvbm5lY3RlZFN0YXJ0VGV4dC5sZW5ndGhcbiAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBzZXRJbnB1dFNlbGVjdGlvbihpbnB1dCwgbG9jYXRpb24pIHtcbiAgaW5wdXQuc2V0U2VsZWN0aW9uUmFuZ2UobG9jYXRpb24sIGxvY2F0aW9uKTtcblxuICAvKipcbiAgICogUmVzZXQgY2FyZXQgaW50byB2aWV3LlxuICAgKiBTaW5jZSB0aGlzIGZ1bmN0aW9uIGFsd2F5cyBjYWxsZWQgYnkgdXNlciBjb250cm9sLCBpdCdzIHNhZmUgdG8gZm9jdXMgZWxlbWVudC5cbiAgICovXG4gIGlucHV0LmJsdXIoKTtcbiAgaW5wdXQuZm9jdXMoKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVNlYXJjaCh0ZXh0LCBzcGxpdCkge1xuICByZXR1cm4gIXNwbGl0IHx8IHRleHQuaW5kZXhPZihzcGxpdCkgPT09IC0xO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGZpbHRlck9wdGlvbihpbnB1dCwgX3JlZikge1xuICB2YXIgX3JlZiR2YWx1ZSA9IF9yZWYudmFsdWUsXG4gICAgdmFsdWUgPSBfcmVmJHZhbHVlID09PSB2b2lkIDAgPyAnJyA6IF9yZWYkdmFsdWU7XG4gIHZhciBsb3dlckNhc2UgPSBpbnB1dC50b0xvd2VyQ2FzZSgpO1xuICByZXR1cm4gdmFsdWUudG9Mb3dlckNhc2UoKS5pbmRleE9mKGxvd2VyQ2FzZSkgIT09IC0xO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-mentions/es/util.js\n");

/***/ })

};
;