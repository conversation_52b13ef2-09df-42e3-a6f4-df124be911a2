"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const children = options.children || ''\n  const className = options.className\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_1__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_3__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_4__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  const mdastTree = processor.parse(file)\n  /** @type {Nodes} */\n  let hastTree = processor.runSync(mdastTree, file)\n\n  // Wrap in `div` if there’s a class name.\n  if (className) {\n    hastTree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        hastTree.type === 'root' ? hastTree.children : [hastTree]\n      )\n    }\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.visit)(hastTree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.toJsxRuntime)(hastTree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;