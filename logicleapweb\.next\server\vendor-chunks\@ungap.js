"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\n\nconst env = typeof self === 'object' ? self : globalThis;\n\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n\n  const unpair = index => {\n    if ($.has(index))\n      return $.get(index);\n\n    const [type, value] = _[index];\n    switch (type) {\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:\n        return as(value, index);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {\n        const arr = as([], index);\n        for (const index of value)\n          arr.push(unpair(index));\n        return arr;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {\n        const object = as({}, index);\n        for (const [key, index] of value)\n          object[unpair(key)] = unpair(index);\n        return object;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n        return as(new Date(value), index);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {\n        const {source, flags} = value;\n        return as(new RegExp(source, flags), index);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {\n        const map = as(new Map, index);\n        for (const [key, index] of value)\n          map.set(unpair(key), unpair(index));\n        return map;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {\n        const set = as(new Set, index);\n        for (const index of value)\n          set.add(unpair(index));\n        return set;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR: {\n        const {name, message} = value;\n        return as(new env[name](message), index);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n    }\n    return as(new env[type](value), index);\n  };\n\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nconst deserialize = serialized => deserializer(new Map, serialized)(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),\n/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\");\n/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\");\n\n\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === \"function\" ?\n  /* c8 ignore start */\n  (any, options) => (\n    options && ('json' in options || 'lossy' in options) ?\n      (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any)\n  ) :\n  (any, options) => (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));\n  /* c8 ignore stop */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ0o7O0FBRXpDO0FBQ0EsYUFBYSxtQkFBbUI7QUFDaEM7O0FBRUE7QUFDQTtBQUNBLFdBQVcsS0FBSztBQUNoQixZQUFZLGtEQUFrRCxHQUFHO0FBQ2pFO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBLE1BQU0sNERBQVcsQ0FBQyx3REFBUztBQUMzQjtBQUNBLG9CQUFvQiw0REFBVyxDQUFDLHdEQUFTLGVBQWUsRUFBQztBQUN6RDs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzPzU4MTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZXNlcmlhbGl6ZX0gZnJvbSAnLi9kZXNlcmlhbGl6ZS5qcyc7XG5pbXBvcnQge3NlcmlhbGl6ZX0gZnJvbSAnLi9zZXJpYWxpemUuanMnO1xuXG4vKipcbiAqIEB0eXBlZGVmIHtBcnJheTxzdHJpbmcsYW55Pn0gUmVjb3JkIGEgdHlwZSByZXByZXNlbnRhdGlvblxuICovXG5cbi8qKlxuICogUmV0dXJucyBhbiBhcnJheSBvZiBzZXJpYWxpemVkIFJlY29yZHMuXG4gKiBAcGFyYW0ge2FueX0gYW55IGEgc2VyaWFsaXphYmxlIHZhbHVlLlxuICogQHBhcmFtIHt7dHJhbnNmZXI/OiBhbnlbXSwganNvbj86IGJvb2xlYW4sIGxvc3N5PzogYm9vbGVhbn0/fSBvcHRpb25zIGFuIG9iamVjdCB3aXRoXG4gKiBhIHRyYW5zZmVyIG9wdGlvbiAoaWdub3JlZCB3aGVuIHBvbHlmaWxsZWQpIGFuZC9vciBub24gc3RhbmRhcmQgZmllbGRzIHRoYXRcbiAqIGZhbGxiYWNrIHRvIHRoZSBwb2x5ZmlsbCBpZiBwcmVzZW50LlxuICogQHJldHVybnMge1JlY29yZFtdfVxuICovXG5leHBvcnQgZGVmYXVsdCB0eXBlb2Ygc3RydWN0dXJlZENsb25lID09PSBcImZ1bmN0aW9uXCIgP1xuICAvKiBjOCBpZ25vcmUgc3RhcnQgKi9cbiAgKGFueSwgb3B0aW9ucykgPT4gKFxuICAgIG9wdGlvbnMgJiYgKCdqc29uJyBpbiBvcHRpb25zIHx8ICdsb3NzeScgaW4gb3B0aW9ucykgP1xuICAgICAgZGVzZXJpYWxpemUoc2VyaWFsaXplKGFueSwgb3B0aW9ucykpIDogc3RydWN0dXJlZENsb25lKGFueSlcbiAgKSA6XG4gIChhbnksIG9wdGlvbnMpID0+IGRlc2VyaWFsaXplKHNlcmlhbGl6ZShhbnksIG9wdGlvbnMpKTtcbiAgLyogYzggaWdub3JlIHN0b3AgKi9cblxuZXhwb3J0IHtkZXNlcmlhbGl6ZSwgc2VyaWFsaXplfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\n\nconst EMPTY = '';\n\nconst {toString} = {};\nconst {keys} = Object;\n\nconst typeOf = value => {\n  const type = typeof value;\n  if (type !== 'object' || !value)\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE, type];\n\n  const asString = toString.call(value).slice(8, -1);\n  switch (asString) {\n    case 'Array':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, EMPTY];\n    case 'Object':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, EMPTY];\n    case 'Date':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.DATE, EMPTY];\n    case 'RegExp':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP, EMPTY];\n    case 'Map':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.MAP, EMPTY];\n    case 'Set':\n      return [_types_js__WEBPACK_IMPORTED_MODULE_0__.SET, EMPTY];\n  }\n\n  if (asString.includes('Array'))\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY, asString];\n\n  if (asString.includes('Error'))\n    return [_types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR, asString];\n\n  return [_types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT, asString];\n};\n\nconst shouldSkip = ([TYPE, type]) => (\n  TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE &&\n  (type === 'function' || type === 'symbol')\n);\n\nconst serializer = (strict, json, $, _) => {\n\n  const as = (out, value) => {\n    const index = _.push(out) - 1;\n    $.set(value, index);\n    return index;\n  };\n\n  const pair = value => {\n    if ($.has(value))\n      return $.get(value);\n\n    let [TYPE, type] = typeOf(value);\n    switch (TYPE) {\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE: {\n        let entry = value;\n        switch (type) {\n          case 'bigint':\n            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;\n            entry = value.toString();\n            break;\n          case 'function':\n          case 'symbol':\n            if (strict)\n              throw new TypeError('unable to serialize ' + type);\n            entry = null;\n            break;\n          case 'undefined':\n            return as([_types_js__WEBPACK_IMPORTED_MODULE_0__.VOID], value);\n        }\n        return as([TYPE, entry], value);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY: {\n        if (type)\n          return as([type, [...value]], value);\n  \n        const arr = [];\n        const index = as([TYPE, arr], value);\n        for (const entry of value)\n          arr.push(pair(entry));\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT: {\n        if (type) {\n          switch (type) {\n            case 'BigInt':\n              return as([type, value.toString()], value);\n            case 'Boolean':\n            case 'Number':\n            case 'String':\n              return as([type, value.valueOf()], value);\n          }\n        }\n\n        if (json && ('toJSON' in value))\n          return pair(value.toJSON());\n\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const key of keys(value)) {\n          if (strict || !shouldSkip(typeOf(value[key])))\n            entries.push([pair(key), pair(value[key])]);\n        }\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n        return as([TYPE, value.toISOString()], value);\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP: {\n        const {source, flags} = value;\n        return as([TYPE, {source, flags}], value);\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const [key, entry] of value) {\n          if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry))))\n            entries.push([pair(key), pair(entry)]);\n        }\n        return index;\n      }\n      case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET: {\n        const entries = [];\n        const index = as([TYPE, entries], value);\n        for (const entry of value) {\n          if (strict || !shouldSkip(typeOf(entry)))\n            entries.push(pair(entry));\n        }\n        return index;\n      }\n    }\n\n    const {message} = value;\n    return as([TYPE, {name: type, message}], value);\n  };\n\n  return pair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */\n const serialize = (value, {json, lossy} = {}) => {\n  const _ = [];\n  return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   BIGINT: () => (/* binding */ BIGINT),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   MAP: () => (/* binding */ MAP),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),\n/* harmony export */   REGEXP: () => (/* binding */ REGEXP),\n/* harmony export */   SET: () => (/* binding */ SET),\n/* harmony export */   VOID: () => (/* binding */ VOID)\n/* harmony export */ });\nconst VOID       = -1;\nconst PRIMITIVE  = 0;\nconst ARRAY      = 1;\nconst OBJECT     = 2;\nconst DATE       = 3;\nconst REGEXP     = 4;\nconst MAP        = 5;\nconst SET        = 6;\nconst ERROR      = 7;\nconst BIGINT     = 8;\n// export const SYMBOL = 9;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL0B1bmdhcC9zdHJ1Y3R1cmVkLWNsb25lL2VzbS90eXBlcy5qcz9jNjA4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBWT0lEICAgICAgID0gLTE7XG5leHBvcnQgY29uc3QgUFJJTUlUSVZFICA9IDA7XG5leHBvcnQgY29uc3QgQVJSQVkgICAgICA9IDE7XG5leHBvcnQgY29uc3QgT0JKRUNUICAgICA9IDI7XG5leHBvcnQgY29uc3QgREFURSAgICAgICA9IDM7XG5leHBvcnQgY29uc3QgUkVHRVhQICAgICA9IDQ7XG5leHBvcnQgY29uc3QgTUFQICAgICAgICA9IDU7XG5leHBvcnQgY29uc3QgU0VUICAgICAgICA9IDY7XG5leHBvcnQgY29uc3QgRVJST1IgICAgICA9IDc7XG5leHBvcnQgY29uc3QgQklHSU5UICAgICA9IDg7XG4vLyBleHBvcnQgY29uc3QgU1lNQk9MID0gOTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\n");

/***/ })

};
;